import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AppNavigator from './src/navigation/AppNavigator';
import { useAppStore } from './src/stores/appStore';
import LoadingSpinner from './src/components/common/LoadingSpinner';
import {
  ErrorBoundary,
  ToastContainer,
  NetworkStatus,
} from './src/components/common';
import { initializePerformanceMonitoring } from './src/utils/performanceUtils';

export default function App() {
  const { isLoading, initializeApp } = useAppStore();

  useEffect(() => {
    // 初始化应用
    initializeApp();

    // 初始化性能监控
    initializePerformanceMonitoring();
  }, []);

  if (isLoading) {
    return (
      <ErrorBoundary>
        <View style={{ flex: 1, backgroundColor: '#F2F2F7' }}>
          <LoadingSpinner text="初始化中..." />
        </View>
      </ErrorBoundary>
    );
  }

  return (
    <SafeAreaProvider>
      <ErrorBoundary
        onError={(error, errorInfo) => {
          console.error('App Error:', error, errorInfo);
          // 这里可以添加错误上报逻辑
        }}
      >
        <View style={{ flex: 1 }}>
          <StatusBar style="auto" />
          <NetworkStatus />
          <AppNavigator />
          <ToastContainer />
        </View>
      </ErrorBoundary>
    </SafeAreaProvider>
  );
}

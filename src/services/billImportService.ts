import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import {
  BillImportSource,
  BillImportResult,
  ImportedBillRecord,
  Transaction,
  BillParseConfig,
} from '../types';

// 支持的账单来源配置
export const BILL_IMPORT_SOURCES: BillImportSource[] = [
  {
    type: 'alipay',
    name: '支付宝账单',
    icon: 'card-outline',
    color: '#1677FF',
    supportedFormats: ['.csv', '.xlsx'],
    description: '支持支付宝导出的CSV和Excel格式账单',
  },
  {
    type: 'wechat',
    name: '微信账单',
    icon: 'chatbubble-outline',
    color: '#07C160',
    supportedFormats: ['.csv', '.xlsx'],
    description: '支持微信支付导出的CSV和Excel格式账单',
  },
  {
    type: 'bank',
    name: '银行账单',
    icon: 'business-outline',
    color: '#FF6B35',
    supportedFormats: ['.csv', '.xlsx', '.txt'],
    description: '支持主流银行导出的账单格式',
  },
];

// 解析配置
const PARSE_CONFIGS: Record<string, BillParseConfig> = {
  alipay: {
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    amountColumn: '金额（元）',
    descriptionColumn: '商品名称',
    typeColumn: '收/支',
    categoryMapping: {
      餐饮美食: '餐饮',
      交通出行: '交通',
      购物消费: '购物',
      生活服务: '生活',
      医疗健康: '医疗',
      教育培训: '教育',
      娱乐休闲: '娱乐',
    },
  },
  wechat: {
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    amountColumn: '金额(元)',
    descriptionColumn: '商品',
    typeColumn: '收/支',
    categoryMapping: {
      餐饮: '餐饮',
      交通: '交通',
      购物: '购物',
      生活缴费: '生活',
      医疗: '医疗',
      教育: '教育',
      娱乐: '娱乐',
    },
  },
  bank: {
    dateFormat: 'YYYY-MM-DD',
    amountColumn: '交易金额',
    descriptionColumn: '交易摘要',
    categoryMapping: {
      转账: '转账',
      消费: '购物',
      取现: '取现',
      存款: '存款',
    },
  },
};

export class BillImportService {
  // 选择并读取账单文件
  static async selectBillFile(): Promise<{
    uri: string;
    content: string;
    name: string;
  }> {
    try {
      if (Platform.OS === 'web') {
        // Web平台文件选择
        return new Promise((resolve, reject) => {
          const input = document.createElement('input');
          input.type = 'file';
          input.accept = '.csv,.xlsx,.txt';

          input.onchange = async (event: any) => {
            const file = event.target.files[0];
            if (!file) {
              reject(new Error('未选择文件'));
              return;
            }

            const reader = new FileReader();
            reader.onload = (e: any) => {
              resolve({
                uri: '',
                content: e.target.result,
                name: file.name,
              });
            };
            reader.onerror = () => {
              reject(new Error('文件读取失败'));
            };
            reader.readAsText(file, 'utf-8');
          };

          input.onerror = () => {
            reject(new Error('文件选择失败'));
          };

          input.click();
        });
      } else {
        // 移动端文件选择
        const result = await DocumentPicker.getDocumentAsync({
          type: [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
          ],
          copyToCacheDirectory: true,
          multiple: false,
        });

        // 检查结果类型 - 新版本API返回结构
        if (result.canceled || !result.assets || result.assets.length === 0) {
          throw new Error('用户取消选择');
        }

        const asset = result.assets[0];
        const content = await FileSystem.readAsStringAsync(asset.uri, {
          encoding: FileSystem.EncodingType.UTF8,
        });

        return {
          uri: asset.uri,
          content,
          name: asset.name,
        };
      }
    } catch (error) {
      console.error('Select bill file error:', error);
      if (error.message === '用户取消选择') {
        throw error;
      }
      throw new Error('选择账单文件失败');
    }
  }

  // 检测账单类型
  static detectBillType(
    content: string,
    fileName: string
  ): BillImportSource | null {
    const lowerFileName = fileName.toLowerCase();
    const lowerContent = content.toLowerCase();

    // 支付宝账单检测
    if (
      lowerFileName.includes('alipay') ||
      lowerContent.includes('支付宝') ||
      lowerContent.includes('alipay') ||
      (lowerContent.includes('商品名称') && lowerContent.includes('收/支'))
    ) {
      return BILL_IMPORT_SOURCES.find((s) => s.type === 'alipay')!;
    }

    // 微信账单检测
    if (
      lowerFileName.includes('wechat') ||
      lowerFileName.includes('微信') ||
      lowerContent.includes('微信支付') ||
      lowerContent.includes('wechat') ||
      (lowerContent.includes('商品') && lowerContent.includes('金额(元)'))
    ) {
      return BILL_IMPORT_SOURCES.find((s) => s.type === 'wechat')!;
    }

    // 银行账单检测（通用）
    if (
      lowerContent.includes('交易日期') ||
      lowerContent.includes('交易金额') ||
      lowerContent.includes('交易摘要') ||
      lowerContent.includes('余额')
    ) {
      return BILL_IMPORT_SOURCES.find((s) => s.type === 'bank')!;
    }

    return null;
  }

  // 解析CSV内容
  static parseCSV(content: string): any[] {
    const lines = content.split('\n').filter((line) => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map((h) => h.trim().replace(/"/g, ''));
    const records = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map((v) => v.trim().replace(/"/g, ''));
      if (values.length === headers.length) {
        const record: any = {};
        headers.forEach((header, index) => {
          record[header] = values[index];
        });
        records.push(record);
      }
    }

    return records;
  }

  // 解析单条记录
  static parseRecord(
    record: any,
    source: BillImportSource,
    userId: string,
    defaultAccountId: string
  ): ImportedBillRecord {
    const config = PARSE_CONFIGS[source.type];
    const errors: string[] = [];
    let confidence = 0.8;

    try {
      // 解析金额
      const amountStr = record[config.amountColumn] || '';
      const amount = Math.abs(parseFloat(amountStr.replace(/[^\d.-]/g, '')));

      if (isNaN(amount) || amount <= 0) {
        errors.push('金额格式无效');
        confidence -= 0.3;
      }

      // 解析交易类型
      let type: 'income' | 'expense' = 'expense';
      if (config.typeColumn && record[config.typeColumn]) {
        const typeStr = record[config.typeColumn];
        if (
          typeStr.includes('收入') ||
          typeStr.includes('入账') ||
          amount > 0
        ) {
          type = 'income';
        }
      }

      // 解析描述
      const description = record[config.descriptionColumn] || '未知交易';

      // 解析日期
      let transactionDate = new Date().toISOString();
      if (record['交易时间'] || record['交易日期']) {
        const dateStr = record['交易时间'] || record['交易日期'];
        const parsedDate = new Date(dateStr);
        if (!isNaN(parsedDate.getTime())) {
          transactionDate = parsedDate.toISOString();
        } else {
          errors.push('日期格式无效');
          confidence -= 0.2;
        }
      }

      const parsedTransaction: Partial<Transaction> = {
        user_id: userId,
        account_id: defaultAccountId,
        amount,
        type,
        description,
        transaction_date: transactionDate,
        is_ai_generated: true,
        ai_confidence: confidence,
      };

      return {
        originalData: record,
        parsedTransaction,
        confidence,
        needsReview: errors.length > 0 || confidence < 0.7,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      return {
        originalData: record,
        parsedTransaction: {
          user_id: userId,
          account_id: defaultAccountId,
          amount: 0,
          type: 'expense',
          description: '解析失败',
          transaction_date: new Date().toISOString(),
          is_ai_generated: true,
          ai_confidence: 0,
        },
        confidence: 0,
        needsReview: true,
        errors: ['记录解析失败'],
      };
    }
  }

  // 导入账单
  static async importBill(
    content: string,
    fileName: string,
    userId: string,
    defaultAccountId: string
  ): Promise<BillImportResult> {
    try {
      // 检测账单类型
      const source = this.detectBillType(content, fileName);
      if (!source) {
        throw new Error('无法识别账单类型，请确认文件格式');
      }

      // 解析CSV内容
      const rawRecords = this.parseCSV(content);
      if (rawRecords.length === 0) {
        throw new Error('文件内容为空或格式不正确');
      }

      // 解析每条记录
      const records: ImportedBillRecord[] = [];
      const errors: string[] = [];
      let successCount = 0;
      let failureCount = 0;

      for (const rawRecord of rawRecords) {
        try {
          const parsedRecord = this.parseRecord(
            rawRecord,
            source,
            userId,
            defaultAccountId
          );
          records.push(parsedRecord);

          if (parsedRecord.errors && parsedRecord.errors.length > 0) {
            failureCount++;
          } else {
            successCount++;
          }
        } catch (error) {
          failureCount++;
          errors.push(`记录解析失败: ${error.message}`);
        }
      }

      return {
        source,
        totalRecords: rawRecords.length,
        successCount,
        failureCount,
        records,
        errors,
      };
    } catch (error) {
      console.error('Import bill error:', error);
      throw new Error(`导入账单失败: ${error.message}`);
    }
  }
}

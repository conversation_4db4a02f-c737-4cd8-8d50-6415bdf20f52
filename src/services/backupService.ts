import { Platform, Alert } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as DocumentPicker from 'expo-document-picker';
import { LocalStorageService } from './localStorageService';
import { Transaction, Account, Category, User } from '../types';

export interface BackupData {
  version: string;
  timestamp: string;
  user: User;
  accounts: Account[];
  categories: Category[];
  transactions: Transaction[];
}

export class BackupService {
  private static readonly BACKUP_VERSION = '1.0.0';
  private static readonly BACKUP_FILE_PREFIX = 'accounting_backup_';

  // 导出数据
  static async exportData(userId: string): Promise<string> {
    try {
      // 获取所有数据
      const user = await LocalStorageService.getCurrentUser();
      if (!user || user.id !== userId) {
        throw new Error('用户信息不匹配');
      }

      const accounts = await LocalStorageService.getAccounts(userId);
      const categories = await LocalStorageService.getCategories(userId);
      const transactions = await LocalStorageService.getTransactions(userId, {
        limit: 10000, // 导出所有交易记录
      });

      // 构建备份数据
      const backupData: BackupData = {
        version: this.BACKUP_VERSION,
        timestamp: new Date().toISOString(),
        user,
        accounts,
        categories,
        transactions,
      };

      // 生成文件名
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `${this.BACKUP_FILE_PREFIX}${timestamp}.json`;

      // 保存到文件
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.writeAsStringAsync(
        fileUri,
        JSON.stringify(backupData, null, 2),
        { encoding: FileSystem.EncodingType.UTF8 }
      );

      return fileUri;
    } catch (error) {
      console.error('Export data error:', error);
      throw new Error('导出数据失败');
    }
  }

  // 分享备份文件
  static async shareBackupFile(fileUri: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Web平台处理
        const response = await fetch(fileUri);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileUri.split('/').pop() || 'backup.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        // 移动端分享
        const isAvailable = await Sharing.isAvailableAsync();
        if (isAvailable) {
          await Sharing.shareAsync(fileUri, {
            mimeType: 'application/json',
            dialogTitle: '分享备份文件',
          });
        } else {
          throw new Error('分享功能不可用');
        }
      }
    } catch (error) {
      console.error('Share backup file error:', error);
      throw new Error('分享备份文件失败');
    }
  }

  // 选择并导入备份文件
  static async importBackupFile(): Promise<BackupData> {
    try {
      let fileUri: string;
      let content: string;

      if (Platform.OS === 'web') {
        // Web平台文件选择
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        return new Promise((resolve, reject) => {
          input.onchange = async (event: any) => {
            const file = event.target.files[0];
            if (!file) {
              reject(new Error('未选择文件'));
              return;
            }

            const reader = new FileReader();
            reader.onload = async (e: any) => {
              try {
                const content = e.target.result;
                const backupData = JSON.parse(content);
                resolve(backupData);
              } catch (error) {
                reject(new Error('文件格式错误'));
              }
            };
            reader.readAsText(file);
          };

          input.click();
        });
      } else {
        // 移动端文件选择
        const result = await DocumentPicker.getDocumentAsync({
          type: 'application/json',
          copyToCacheDirectory: true,
          multiple: false,
        });

        // 检查结果类型 - 新版本API返回结构
        if (result.canceled || !result.assets || result.assets.length === 0) {
          throw new Error('用户取消选择');
        }

        const asset = result.assets[0];
        fileUri = asset.uri;
        content = await FileSystem.readAsStringAsync(fileUri, {
          encoding: FileSystem.EncodingType.UTF8,
        });

        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Import backup file error:', error);
      throw new Error('导入备份文件失败');
    }
  }

  // 验证备份数据
  static validateBackupData(data: any): data is BackupData {
    try {
      return (
        data &&
        typeof data === 'object' &&
        typeof data.version === 'string' &&
        typeof data.timestamp === 'string' &&
        data.user &&
        Array.isArray(data.accounts) &&
        Array.isArray(data.categories) &&
        Array.isArray(data.transactions)
      );
    } catch {
      return false;
    }
  }

  // 恢复数据
  static async restoreData(
    backupData: BackupData,
    userId: string
  ): Promise<void> {
    try {
      if (!this.validateBackupData(backupData)) {
        throw new Error('备份数据格式无效');
      }

      // 确认恢复操作
      return new Promise((resolve, reject) => {
        Alert.alert(
          '确认恢复',
          '恢复备份将覆盖当前所有数据，此操作无法撤销。确定要继续吗？',
          [
            {
              text: '取消',
              style: 'cancel',
              onPress: () => reject(new Error('用户取消')),
            },
            {
              text: '确认恢复',
              style: 'destructive',
              onPress: async () => {
                try {
                  // 更新用户ID
                  const updatedAccounts = backupData.accounts.map(
                    (account) => ({
                      ...account,
                      user_id: userId,
                    })
                  );

                  const updatedCategories = backupData.categories.map(
                    (category) => ({
                      ...category,
                      user_id: userId,
                    })
                  );

                  const updatedTransactions = backupData.transactions.map(
                    (transaction) => ({
                      ...transaction,
                      user_id: userId,
                    })
                  );

                  // 保存数据
                  await LocalStorageService.saveAccounts(updatedAccounts);
                  await LocalStorageService.saveCategories(updatedCategories);
                  await LocalStorageService.saveTransactions(
                    updatedTransactions
                  );

                  resolve();
                } catch (error) {
                  reject(error);
                }
              },
            },
          ]
        );
      });
    } catch (error) {
      console.error('Restore data error:', error);
      throw new Error('恢复数据失败');
    }
  }

  // 获取备份信息
  static getBackupInfo(backupData: BackupData) {
    return {
      version: backupData.version,
      timestamp: backupData.timestamp,
      accountCount: backupData.accounts.length,
      categoryCount: backupData.categories.length,
      transactionCount: backupData.transactions.length,
      userName: backupData.user.name || backupData.user.email,
    };
  }

  // 清理临时文件
  static async cleanupTempFiles(): Promise<void> {
    try {
      const documentDir = FileSystem.documentDirectory;
      if (!documentDir) return;

      const files = await FileSystem.readDirectoryAsync(documentDir);
      const backupFiles = files.filter((file) =>
        file.startsWith(this.BACKUP_FILE_PREFIX)
      );

      // 删除超过7天的备份文件
      const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

      for (const file of backupFiles) {
        const filePath = `${documentDir}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath);

        if (
          fileInfo.exists &&
          fileInfo.modificationTime &&
          fileInfo.modificationTime < sevenDaysAgo
        ) {
          await FileSystem.deleteAsync(filePath);
        }
      }
    } catch (error) {
      console.error('Cleanup temp files error:', error);
    }
  }
}

import React, { useMemo, useCallback } from 'react';
import { FlatList, FlatListProps, ListRenderItem } from 'react-native';

interface VirtualizedListProps<T> extends Omit<FlatListProps<T>, 'renderItem' | 'data'> {
  data: T[];
  renderItem: ListRenderItem<T>;
  itemHeight?: number;
  windowSize?: number;
  maxToRenderPerBatch?: number;
  updateCellsBatchingPeriod?: number;
  initialNumToRender?: number;
  removeClippedSubviews?: boolean;
}

function VirtualizedList<T>({
  data,
  renderItem,
  itemHeight = 80,
  windowSize = 10,
  maxToRenderPerBatch = 10,
  updateCellsBatchingPeriod = 50,
  initialNumToRender = 10,
  removeClippedSubviews = true,
  ...props
}: VirtualizedListProps<T>) {
  // 使用 useMemo 缓存数据，避免不必要的重新渲染
  const memoizedData = useMemo(() => data, [data]);

  // 使用 useCallback 缓存渲染函数
  const memoizedRenderItem = useCallback(renderItem, [renderItem]);

  // 获取项目布局的优化函数
  const getItemLayout = useCallback(
    (data: T[] | null | undefined, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }),
    [itemHeight]
  );

  // 键提取器
  const keyExtractor = useCallback(
    (item: T, index: number) => {
      // 如果项目有 id 属性，使用它作为键
      if (item && typeof item === 'object' && 'id' in item) {
        return String((item as any).id);
      }
      return String(index);
    },
    []
  );

  return (
    <FlatList
      data={memoizedData}
      renderItem={memoizedRenderItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      windowSize={windowSize}
      maxToRenderPerBatch={maxToRenderPerBatch}
      updateCellsBatchingPeriod={updateCellsBatchingPeriod}
      initialNumToRender={initialNumToRender}
      removeClippedSubviews={removeClippedSubviews}
      // 性能优化配置
      disableVirtualization={false}
      legacyImplementation={false}
      {...props}
    />
  );
}

export default React.memo(VirtualizedList) as typeof VirtualizedList;

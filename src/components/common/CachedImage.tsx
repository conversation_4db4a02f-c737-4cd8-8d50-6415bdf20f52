import React, { useState, useEffect, useCallback } from 'react';
import { Image, ImageProps, View, StyleSheet, ActivityIndicator } from 'react-native';
import * as FileSystem from 'expo-file-system';

interface CachedImageProps extends Omit<ImageProps, 'source'> {
  uri: string;
  cacheKey?: string;
  placeholder?: React.ReactNode;
  fallback?: React.ReactNode;
  showLoading?: boolean;
  cacheDuration?: number; // 缓存持续时间（毫秒）
}

interface CacheInfo {
  uri: string;
  timestamp: number;
  localPath: string;
}

class ImageCache {
  private static cache = new Map<string, CacheInfo>();
  private static readonly CACHE_DIR = `${FileSystem.cacheDirectory}images/`;

  static async ensureCacheDir() {
    const dirInfo = await FileSystem.getInfoAsync(this.CACHE_DIR);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(this.CACHE_DIR, { intermediates: true });
    }
  }

  static getCacheKey(uri: string, customKey?: string): string {
    return customKey || uri.split('/').pop() || 'unknown';
  }

  static async getCachedImage(
    uri: string, 
    cacheKey?: string, 
    cacheDuration = 24 * 60 * 60 * 1000 // 24小时
  ): Promise<string | null> {
    const key = this.getCacheKey(uri, cacheKey);
    const cached = this.cache.get(key);

    if (cached) {
      // 检查缓存是否过期
      if (Date.now() - cached.timestamp < cacheDuration) {
        // 检查文件是否仍然存在
        const fileInfo = await FileSystem.getInfoAsync(cached.localPath);
        if (fileInfo.exists) {
          return cached.localPath;
        }
      }
      // 缓存过期或文件不存在，移除缓存记录
      this.cache.delete(key);
    }

    return null;
  }

  static async cacheImage(uri: string, cacheKey?: string): Promise<string> {
    await this.ensureCacheDir();
    
    const key = this.getCacheKey(uri, cacheKey);
    const extension = uri.split('.').pop() || 'jpg';
    const localPath = `${this.CACHE_DIR}${key}.${extension}`;

    try {
      const downloadResult = await FileSystem.downloadAsync(uri, localPath);
      
      if (downloadResult.status === 200) {
        this.cache.set(key, {
          uri,
          timestamp: Date.now(),
          localPath: downloadResult.uri,
        });
        return downloadResult.uri;
      } else {
        throw new Error(`Download failed with status ${downloadResult.status}`);
      }
    } catch (error) {
      console.warn('Failed to cache image:', error);
      throw error;
    }
  }

  static async clearExpiredCache(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7天
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, info] of this.cache.entries()) {
      if (now - info.timestamp > maxAge) {
        expiredKeys.push(key);
        try {
          await FileSystem.deleteAsync(info.localPath, { idempotent: true });
        } catch (error) {
          console.warn('Failed to delete cached file:', error);
        }
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }
}

const CachedImage: React.FC<CachedImageProps> = ({
  uri,
  cacheKey,
  placeholder,
  fallback,
  showLoading = true,
  cacheDuration,
  style,
  ...props
}) => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const loadImage = useCallback(async () => {
    if (!uri) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    try {
      setIsLoading(true);
      setHasError(false);

      // 首先尝试从缓存获取
      const cachedUri = await ImageCache.getCachedImage(uri, cacheKey, cacheDuration);
      
      if (cachedUri) {
        setImageUri(cachedUri);
        setIsLoading(false);
      } else {
        // 缓存中没有，下载并缓存
        const downloadedUri = await ImageCache.cacheImage(uri, cacheKey);
        setImageUri(downloadedUri);
        setIsLoading(false);
      }
    } catch (error) {
      console.warn('Failed to load cached image:', error);
      // 如果缓存失败，直接使用原始URI
      setImageUri(uri);
      setIsLoading(false);
    }
  }, [uri, cacheKey, cacheDuration]);

  useEffect(() => {
    loadImage();
  }, [loadImage]);

  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
  }, []);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
  }, []);

  if (hasError && fallback) {
    return <>{fallback}</>;
  }

  if (isLoading) {
    if (placeholder) {
      return <>{placeholder}</>;
    }
    
    if (showLoading) {
      return (
        <View style={[styles.loadingContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      );
    }
  }

  if (!imageUri) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <Image
      {...props}
      source={{ uri: imageUri }}
      style={style}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
  },
});

export default React.memo(CachedImage);
export { ImageCache };

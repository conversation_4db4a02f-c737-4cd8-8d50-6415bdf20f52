import React, { useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  Animated,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface EnhancedLoadingSpinnerProps {
  text?: string;
  size?: 'small' | 'large';
  color?: string;
  overlay?: boolean;
  modal?: boolean;
  progress?: number; // 0-100
  showProgress?: boolean;
  onCancel?: () => void;
  cancelable?: boolean;
  timeout?: number; // 超时时间（毫秒）
  onTimeout?: () => void;
}

const EnhancedLoadingSpinner: React.FC<EnhancedLoadingSpinnerProps> = ({
  text = '加载中...',
  size = 'large',
  color = '#007AFF',
  overlay = false,
  modal = false,
  progress,
  showProgress = false,
  onCancel,
  cancelable = false,
  timeout,
  onTimeout,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // 入场动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // 设置超时
    if (timeout && onTimeout) {
      timeoutRef.current = setTimeout(() => {
        onTimeout();
      }, timeout);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [timeout, onTimeout]);

  const renderProgressBar = () => {
    if (!showProgress || progress === undefined) return null;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${Math.max(0, Math.min(100, progress))}%`,
                backgroundColor: color,
              }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>{Math.round(progress)}%</Text>
      </View>
    );
  };

  const renderContent = () => (
    <Animated.View
      style={[
        styles.content,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <ActivityIndicator size={size} color={color} />
      {text && <Text style={[styles.text, { color: overlay || modal ? '#FFFFFF' : '#8E8E93' }]}>{text}</Text>}
      {renderProgressBar()}
      
      {cancelable && onCancel && (
        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
          <Ionicons name="close" size={20} color={overlay || modal ? '#FFFFFF' : '#8E8E93'} />
          <Text style={[styles.cancelText, { color: overlay || modal ? '#FFFFFF' : '#8E8E93' }]}>
            取消
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  if (modal) {
    return (
      <Modal
        transparent
        visible={true}
        animationType="none"
        onRequestClose={cancelable ? onCancel : undefined}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {renderContent()}
          </View>
        </View>
      </Modal>
    );
  }

  if (overlay) {
    return (
      <View style={styles.overlayContainer}>
        {renderContent()}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    minWidth: 200,
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  progressContainer: {
    marginTop: 16,
    width: 200,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    marginTop: 8,
    fontSize: 12,
    color: '#8E8E93',
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    gap: 4,
  },
  cancelText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default EnhancedLoadingSpinner;

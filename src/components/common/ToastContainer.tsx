import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Toast, { ToastManager, ToastType } from './Toast';

interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
  action?: { label: string; onPress: () => void };
}

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  useEffect(() => {
    const handleToastsChange = (newToasts: ToastItem[]) => {
      setToasts(newToasts);
    };

    ToastManager.addListener(handleToastsChange);

    return () => {
      ToastManager.removeListener(handleToastsChange);
    };
  }, []);

  const handleHideToast = (id: string) => {
    ToastManager.hide(id);
  };

  return (
    <View style={styles.container} pointerEvents="box-none">
      {toasts.map((toast, index) => (
        <Toast
          key={toast.id}
          visible={true}
          message={toast.message}
          type={toast.type}
          duration={0} // 由ToastManager控制持续时间
          onHide={() => handleHideToast(toast.id)}
          action={toast.action}
          position="top"
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
});

export default ToastContainer;

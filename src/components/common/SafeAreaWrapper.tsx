import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  StatusBar,
  Platform,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  backgroundColor?: string;
  statusBarStyle?: 'light-content' | 'dark-content' | 'default';
  statusBarBackgroundColor?: string;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
  forceInsets?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
}

const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  style,
  backgroundColor = '#F2F2F7',
  statusBarStyle = 'dark-content',
  statusBarBackgroundColor,
  edges = ['top', 'bottom', 'left', 'right'],
  forceInsets,
}) => {
  const insets = useSafeAreaInsets();
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  // 检测是否为异形屏（刘海屏、打孔屏等）
  const hasNotch = insets.top > 20;
  const hasBottomInset = insets.bottom > 0;

  // 计算实际的安全区域
  const safeAreaInsets = {
    top: edges.includes('top') ? (forceInsets?.top ?? insets.top) : 0,
    bottom: edges.includes('bottom') ? (forceInsets?.bottom ?? insets.bottom) : 0,
    left: edges.includes('left') ? (forceInsets?.left ?? insets.left) : 0,
    right: edges.includes('right') ? (forceInsets?.right ?? insets.right) : 0,
  };

  const containerStyle: ViewStyle = {
    flex: 1,
    backgroundColor,
    paddingTop: safeAreaInsets.top,
    paddingBottom: safeAreaInsets.bottom,
    paddingLeft: safeAreaInsets.left,
    paddingRight: safeAreaInsets.right,
  };

  return (
    <>
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={statusBarBackgroundColor || backgroundColor}
        translucent={Platform.OS === 'android'}
      />
      <View style={[containerStyle, style]}>
        {children}
      </View>
    </>
  );
};

// 获取安全区域信息的Hook
export const useSafeAreaInfo = () => {
  const insets = useSafeAreaInsets();
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  return {
    insets,
    screenWidth,
    screenHeight,
    hasNotch: insets.top > 20,
    hasBottomInset: insets.bottom > 0,
    isLandscape: screenWidth > screenHeight,
    // 常用的安全区域值
    statusBarHeight: insets.top,
    bottomSafeArea: insets.bottom,
    // 计算内容区域尺寸
    contentWidth: screenWidth - insets.left - insets.right,
    contentHeight: screenHeight - insets.top - insets.bottom,
  };
};

// 创建带安全区域的样式
export const createSafeAreaStyle = (
  baseStyle: ViewStyle,
  edges: ('top' | 'bottom' | 'left' | 'right')[] = ['top', 'bottom']
) => {
  const insets = useSafeAreaInsets();
  
  const safeAreaStyle: ViewStyle = {};
  
  if (edges.includes('top')) {
    safeAreaStyle.paddingTop = (baseStyle.paddingTop as number || 0) + insets.top;
  }
  if (edges.includes('bottom')) {
    safeAreaStyle.paddingBottom = (baseStyle.paddingBottom as number || 0) + insets.bottom;
  }
  if (edges.includes('left')) {
    safeAreaStyle.paddingLeft = (baseStyle.paddingLeft as number || 0) + insets.left;
  }
  if (edges.includes('right')) {
    safeAreaStyle.paddingRight = (baseStyle.paddingRight as number || 0) + insets.right;
  }

  return [baseStyle, safeAreaStyle];
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaWrapper;

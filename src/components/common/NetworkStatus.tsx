import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NetInfo from '@react-native-community/netinfo';

interface NetworkStatusProps {
  showWhenConnected?: boolean;
  position?: 'top' | 'bottom';
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({
  showWhenConnected = false,
  position = 'top',
}) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const slideAnim = useState(new Animated.Value(-50))[0];

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
      setConnectionType(state.type);
      
      if (state.isConnected === false || showWhenConnected) {
        showBanner();
      } else {
        hideBanner();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [showWhenConnected]);

  const showBanner = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const hideBanner = () => {
    Animated.timing(slideAnim, {
      toValue: position === 'top' ? -50 : 50,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const getStatusConfig = () => {
    if (isConnected === null) {
      return {
        backgroundColor: '#8E8E93',
        icon: 'help-circle' as const,
        text: '检查网络状态...',
        textColor: '#FFFFFF',
      };
    }

    if (isConnected) {
      return {
        backgroundColor: '#34C759',
        icon: 'wifi' as const,
        text: `已连接 (${connectionType})`,
        textColor: '#FFFFFF',
      };
    }

    return {
      backgroundColor: '#FF3B30',
      icon: 'wifi-off' as const,
      text: '网络连接已断开',
      textColor: '#FFFFFF',
    };
  };

  const config = getStatusConfig();

  // 如果已连接且不需要显示连接状态，则不渲染
  if (isConnected && !showWhenConnected) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: config.backgroundColor,
          transform: [{ translateY: slideAnim }],
          [position]: 0,
        },
      ]}
    >
      <View style={styles.content}>
        <Ionicons
          name={config.icon}
          size={16}
          color={config.textColor}
          style={styles.icon}
        />
        <Text style={[styles.text, { color: config.textColor }]}>
          {config.text}
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    paddingVertical: 8,
    paddingHorizontal: 16,
    zIndex: 1000,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 8,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NetworkStatus;

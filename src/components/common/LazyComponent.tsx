import React, { useState, useEffect, useRef, ComponentType } from 'react';
import { View, ViewStyle, Dimensions } from 'react-native';
import { runAfterInteractions } from '../../utils/performanceUtils';

interface LazyComponentProps {
  children: React.ReactNode;
  placeholder?: React.ReactNode;
  threshold?: number; // 距离视口多少像素时开始加载
  style?: ViewStyle;
  delay?: number; // 延迟加载时间（毫秒）
}

const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  placeholder,
  threshold = 100,
  style,
  delay = 0,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const viewRef = useRef<View>(null);

  useEffect(() => {
    if (!isVisible) return;

    const loadComponent = async () => {
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      await runAfterInteractions(() => {
        setShouldRender(true);
      });
    };

    loadComponent();
  }, [isVisible, delay]);

  const handleLayout = () => {
    if (viewRef.current) {
      viewRef.current.measure((x, y, width, height, pageX, pageY) => {
        const screenHeight = Dimensions.get('window').height;
        const isInViewport = pageY < screenHeight + threshold && pageY + height > -threshold;
        
        if (isInViewport && !isVisible) {
          setIsVisible(true);
        }
      });
    }
  };

  return (
    <View
      ref={viewRef}
      style={style}
      onLayout={handleLayout}
    >
      {shouldRender ? children : placeholder}
    </View>
  );
};

// 高阶组件版本的懒加载
export function withLazyLoading<P extends object>(
  Component: ComponentType<P>,
  placeholder?: React.ReactNode,
  delay?: number
) {
  return React.memo((props: P) => {
    const [shouldRender, setShouldRender] = useState(false);

    useEffect(() => {
      const loadComponent = async () => {
        if (delay) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        await runAfterInteractions(() => {
          setShouldRender(true);
        });
      };

      loadComponent();
    }, []);

    if (!shouldRender) {
      return placeholder ? <>{placeholder}</> : null;
    }

    return <Component {...props} />;
  });
}

export default React.memo(LazyComponent);

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import {
  <PERSON>,
  But<PERSON>,
  LoadingSpinner,
  ToastManager,
} from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { BackupService, BackupData } from '../../services/backupService';

const DataBackupScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAppStore();
  const { loadAccounts, loadCategories, loadTransactions } =
    useTransactionStore();

  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const handleExportData = async () => {
    if (!user) {
      Alert.alert('错误', '用户信息不存在');
      return;
    }

    setIsExporting(true);
    try {
      const fileUri = await BackupService.exportData(user.id);

      ToastManager.success('数据导出成功', 5000, {
        label: '分享',
        onPress: async () => {
          try {
            await BackupService.shareBackupFile(fileUri);
            ToastManager.success('文件分享成功');
          } catch (error) {
            ToastManager.error('无法分享备份文件');
          }
        },
      });
    } catch (error) {
      Alert.alert('导出失败', '导出数据时发生错误，请重试');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportData = async () => {
    if (!user) {
      Alert.alert('错误', '用户信息不存在');
      return;
    }

    setIsImporting(true);
    try {
      const backupData: BackupData = await BackupService.importBackupFile();

      if (!BackupService.validateBackupData(backupData)) {
        Alert.alert('导入失败', '备份文件格式无效');
        return;
      }

      const backupInfo = BackupService.getBackupInfo(backupData);

      Alert.alert(
        '确认导入',
        `备份信息：
• 版本：${backupInfo.version}
• 时间：${new Date(backupInfo.timestamp).toLocaleString()}
• 用户：${backupInfo.userName}
• 账户：${backupInfo.accountCount} 个
• 分类：${backupInfo.categoryCount} 个
• 交易：${backupInfo.transactionCount} 笔

导入将覆盖当前所有数据，确定要继续吗？`,
        [
          { text: '取消', style: 'cancel' },
          {
            text: '确认导入',
            style: 'destructive',
            onPress: async () => {
              try {
                await BackupService.restoreData(backupData, user.id);

                // 重新加载数据
                await Promise.all([
                  loadAccounts(user.id),
                  loadCategories(user.id),
                  loadTransactions(user.id, true),
                ]);

                Alert.alert('导入成功', '数据已成功恢复');
              } catch (error) {
                Alert.alert('导入失败', '恢复数据时发生错误');
              }
            },
          },
        ]
      );
    } catch (error) {
      if (error.message !== '用户取消选择' && error.message !== '用户取消') {
        Alert.alert('导入失败', '选择或读取备份文件时发生错误');
      }
    } finally {
      setIsImporting(false);
    }
  };

  const handleCleanupFiles = async () => {
    try {
      await BackupService.cleanupTempFiles();
      Alert.alert('清理完成', '已清理过期的临时文件');
    } catch (error) {
      Alert.alert('清理失败', '清理临时文件时发生错误');
    }
  };

  const renderFeatureCard = (
    title: string,
    description: string,
    icon: keyof typeof Ionicons.glyphMap,
    color: string,
    onPress: () => void,
    loading?: boolean
  ) => (
    <Card style={styles.featureCard}>
      <TouchableOpacity
        style={styles.featureContent}
        onPress={onPress}
        disabled={loading}
      >
        <View style={[styles.featureIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon} size={32} color={color} />
        </View>

        <View style={styles.featureInfo}>
          <Text style={styles.featureTitle}>{title}</Text>
          <Text style={styles.featureDescription}>{description}</Text>
        </View>

        <View style={styles.featureAction}>
          {loading ? (
            <LoadingSpinner size="small" />
          ) : (
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          )}
        </View>
      </TouchableOpacity>
    </Card>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>数据备份</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>数据管理</Text>
          <Text style={styles.sectionDescription}>
            备份和恢复您的记账数据，确保数据安全
          </Text>
        </View>

        {renderFeatureCard(
          '导出数据',
          '将所有记账数据导出为备份文件',
          'download-outline',
          '#34C759',
          handleExportData,
          isExporting
        )}

        {renderFeatureCard(
          '导入数据',
          '从备份文件恢复记账数据',
          'cloud-upload-outline',
          '#007AFF',
          handleImportData,
          isImporting
        )}

        {renderFeatureCard(
          '清理文件',
          '清理过期的临时备份文件',
          'trash-outline',
          '#FF9500',
          handleCleanupFiles
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>注意事项</Text>
          <View style={styles.notesList}>
            <View style={styles.noteItem}>
              <Ionicons name="information-circle" size={16} color="#007AFF" />
              <Text style={styles.noteText}>
                导出的备份文件包含所有账户、分类和交易数据
              </Text>
            </View>
            <View style={styles.noteItem}>
              <Ionicons name="warning" size={16} color="#FF9500" />
              <Text style={styles.noteText}>
                导入数据将覆盖当前所有数据，请谨慎操作
              </Text>
            </View>
            <View style={styles.noteItem}>
              <Ionicons name="shield-checkmark" size={16} color="#34C759" />
              <Text style={styles.noteText}>
                建议定期备份数据，以防数据丢失
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#8E8E93',
    lineHeight: 22,
  },
  featureCard: {
    marginBottom: 16,
    padding: 0,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  featureIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureInfo: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
  featureAction: {
    marginLeft: 12,
  },
  notesList: {
    gap: 12,
  },
  noteItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
  },
});

export default DataBackupScreen;

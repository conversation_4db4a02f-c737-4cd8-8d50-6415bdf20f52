import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { Card, Button } from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user, theme, currency, language, updateSettings, signOut } =
    useAppStore();
  const { accounts, categories, loadAccounts, loadCategories } =
    useTransactionStore();

  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  useEffect(() => {
    if (user) {
      loadAccounts(user.id);
      loadCategories(user.id);
    }
  }, [user]);

  const handleSignOut = () => {
    Alert.alert('确认退出', '确定要退出登录吗？', [
      { text: '取消', style: 'cancel' },
      {
        text: '退出',
        style: 'destructive',
        onPress: async () => {
          try {
            await signOut();
            // 这里可以导航到登录页面
          } catch (error) {
            Alert.alert('错误', '退出失败，请重试');
          }
        },
      },
    ]);
  };

  const handleDataBackup = () => {
    navigation.navigate('DataBackup' as never);
  };

  const handleBillImport = () => {
    navigation.navigate('BillImport' as never);
  };

  const settingSections = [
    {
      title: '账户管理',
      items: [
        {
          icon: 'wallet-outline',
          title: '我的账户',
          subtitle: `${accounts.length} 个账户`,
          onPress: () => navigation.navigate('AccountManagement' as never),
          showArrow: true,
        },
        {
          icon: 'pricetag-outline',
          title: '分类管理',
          subtitle: `${categories.length} 个分类`,
          onPress: () => navigation.navigate('CategoryManagement' as never),
          showArrow: true,
        },
      ],
    },
    {
      title: '应用设置',
      items: [
        {
          icon: 'color-palette-outline',
          title: '主题设置',
          subtitle:
            theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '跟随系统',
          onPress: () => {
            Alert.alert('功能开发中', '主题设置功能正在开发中');
          },
          showArrow: true,
        },
        {
          icon: 'globe-outline',
          title: '货币设置',
          subtitle: currency,
          onPress: () => {
            Alert.alert('功能开发中', '货币设置功能正在开发中');
          },
          showArrow: true,
        },
        {
          icon: 'language-outline',
          title: '语言设置',
          subtitle: language === 'zh-CN' ? '简体中文' : language,
          onPress: () => {
            Alert.alert('功能开发中', '语言设置功能正在开发中');
          },
          showArrow: true,
        },
      ],
    },
    {
      title: '安全与隐私',
      items: [
        {
          icon: 'notifications-outline',
          title: '推送通知',
          subtitle: '接收记账提醒',
          rightComponent: (
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
              thumbColor="#FFFFFF"
            />
          ),
        },
        {
          icon: 'finger-print-outline',
          title: '生物识别',
          subtitle: '使用指纹或面容ID解锁',
          rightComponent: (
            <Switch
              value={biometricEnabled}
              onValueChange={setBiometricEnabled}
              trackColor={{ false: '#E5E5EA', true: '#007AFF' }}
              thumbColor="#FFFFFF"
            />
          ),
        },
      ],
    },
    {
      title: '数据管理',
      items: [
        {
          icon: 'download-outline',
          title: '导入账单',
          subtitle: '支持支付宝、微信、银行账单',
          onPress: handleBillImport,
          showArrow: true,
        },
        {
          icon: 'cloud-outline',
          title: '数据备份',
          subtitle: '备份和恢复记账数据',
          onPress: handleDataBackup,
          showArrow: true,
        },
      ],
    },
    {
      title: '关于',
      items: [
        {
          icon: 'information-circle-outline',
          title: '应用版本',
          subtitle: '1.0.0',
          showArrow: false,
        },
        {
          icon: 'help-circle-outline',
          title: '帮助与反馈',
          subtitle: '使用帮助和问题反馈',
          onPress: () => {
            Alert.alert('功能开发中', '帮助与反馈功能正在开发中');
          },
          showArrow: true,
        },
        {
          icon: 'document-text-outline',
          title: '隐私政策',
          subtitle: '查看隐私政策',
          onPress: () => {
            Alert.alert('功能开发中', '隐私政策功能正在开发中');
          },
          showArrow: true,
        },
      ],
    },
  ];

  const renderSettingItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.settingItem}
      onPress={item.onPress}
      disabled={!item.onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.iconContainer}>
          <Ionicons name={item.icon} size={20} color="#007AFF" />
        </View>
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          {item.subtitle && (
            <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>

      <View style={styles.settingRight}>
        {item.rightComponent ||
          (item.showArrow && (
            <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
          ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 用户信息卡片 */}
        <Card style={styles.userCard}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Ionicons name="person" size={32} color="#FFFFFF" />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user?.name || '用户'}</Text>
              <Text style={styles.userEmail}>
                {user?.email || '<EMAIL>'}
              </Text>
            </View>
          </View>
        </Card>

        {/* 设置选项 */}
        {settingSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <Card style={styles.sectionCard}>
              {section.items.map((item, itemIndex) => (
                <View key={itemIndex}>
                  {renderSettingItem(item, itemIndex)}
                  {itemIndex < section.items.length - 1 && (
                    <View style={styles.separator} />
                  )}
                </View>
              ))}
            </Card>
          </View>
        ))}

        {/* 退出登录按钮 */}
        <View style={styles.signOutSection}>
          <Button
            title="退出登录"
            variant="danger"
            onPress={handleSignOut}
            style={styles.signOutButton}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  userCard: {
    margin: 20,
    padding: 20,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: '#8E8E93',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
    marginLeft: 4,
  },
  sectionCard: {
    padding: 0,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
  settingRight: {
    marginLeft: 12,
  },
  separator: {
    height: 1,
    backgroundColor: '#F2F2F7',
    marginLeft: 60,
  },
  signOutSection: {
    margin: 20,
    marginTop: 0,
    marginBottom: 40,
  },
  signOutButton: {
    marginTop: 20,
  },
});

export default SettingsScreen;

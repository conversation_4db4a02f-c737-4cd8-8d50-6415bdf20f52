import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../stores/appStore';
import { BillImportResult, ImportedBillRecord, Transaction } from '../../types';
import { LocalStorageService } from '../../services/localStorageService';
import { ToastManager } from '../../utils/toastManager';

interface Props {
  route: {
    params: {
      result: BillImportResult;
    };
  };
  navigation: any;
}

export default function BillImportPreviewScreen({ route, navigation }: Props) {
  const { result } = route.params;
  const { user, loadTransactions } = useAppStore();
  const [isImporting, setIsImporting] = useState(false);
  const [selectedRecords, setSelectedRecords] = useState<Set<number>>(
    new Set(result.records.map((_, index) => index))
  );

  const stats = useMemo(() => {
    const selected = result.records.filter((_, index) => selectedRecords.has(index));
    const needsReview = selected.filter(r => r.needsReview).length;
    const totalAmount = selected.reduce((sum, r) => sum + (r.parsedTransaction.amount || 0), 0);
    
    return {
      selectedCount: selected.length,
      needsReview,
      totalAmount,
      incomeCount: selected.filter(r => r.parsedTransaction.type === 'income').length,
      expenseCount: selected.filter(r => r.parsedTransaction.type === 'expense').length,
    };
  }, [selectedRecords, result.records]);

  const toggleRecord = (index: number) => {
    const newSelected = new Set(selectedRecords);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedRecords(newSelected);
  };

  const toggleAll = () => {
    if (selectedRecords.size === result.records.length) {
      setSelectedRecords(new Set());
    } else {
      setSelectedRecords(new Set(result.records.map((_, index) => index)));
    }
  };

  const handleImport = async () => {
    if (!user) {
      Alert.alert('错误', '用户信息不存在');
      return;
    }

    if (selectedRecords.size === 0) {
      Alert.alert('提示', '请至少选择一条记录进行导入');
      return;
    }

    const hasReviewItems = result.records
      .filter((_, index) => selectedRecords.has(index))
      .some(r => r.needsReview);

    if (hasReviewItems) {
      Alert.alert(
        '确认导入',
        `有 ${stats.needsReview} 条记录需要人工确认，是否继续导入？`,
        [
          { text: '取消', style: 'cancel' },
          { text: '继续导入', onPress: performImport }
        ]
      );
    } else {
      performImport();
    }
  };

  const performImport = async () => {
    setIsImporting(true);
    try {
      const selectedTransactions = result.records
        .filter((_, index) => selectedRecords.has(index))
        .map(record => ({
          ...record.parsedTransaction,
          id: `import_${Date.now()}_${Math.random()}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        } as Transaction));

      // 保存到本地存储
      const existingTransactions = await LocalStorageService.getTransactions(user.id, { limit: 10000 });
      const allTransactions = [...existingTransactions, ...selectedTransactions];
      await LocalStorageService.saveTransactions(allTransactions);

      // 重新加载数据
      await loadTransactions(user.id, true);

      ToastManager.success(`成功导入 ${selectedTransactions.length} 条记录`);
      navigation.navigate('Settings');
    } catch (error) {
      console.error('Import error:', error);
      Alert.alert('导入失败', '保存交易记录时发生错误');
    } finally {
      setIsImporting(false);
    }
  };

  const renderRecord = ({ item, index }: { item: ImportedBillRecord; index: number }) => {
    const isSelected = selectedRecords.has(index);
    const transaction = item.parsedTransaction;
    
    return (
      <TouchableOpacity
        style={[styles.recordCard, isSelected && styles.selectedCard]}
        onPress={() => toggleRecord(index)}
      >
        <View style={styles.recordHeader}>
          <View style={styles.recordInfo}>
            <Text style={styles.recordDescription}>
              {transaction.description || '未知交易'}
            </Text>
            <Text style={styles.recordDate}>
              {new Date(transaction.transaction_date || '').toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.recordAmount}>
            <Text style={[
              styles.amountText,
              { color: transaction.type === 'income' ? '#34C759' : '#FF3B30' }
            ]}>
              {transaction.type === 'income' ? '+' : '-'}¥{transaction.amount?.toFixed(2)}
            </Text>
            {item.needsReview && (
              <View style={styles.reviewBadge}>
                <Text style={styles.reviewText}>需确认</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.recordFooter}>
          <View style={styles.checkbox}>
            {isSelected && <Ionicons name="checkmark" size={16} color="#007AFF" />}
          </View>
          
          {item.errors && item.errors.length > 0 && (
            <View style={styles.errorContainer}>
              <Ionicons name="warning-outline" size={14} color="#FF9500" />
              <Text style={styles.errorText}>{item.errors[0]}</Text>
            </View>
          )}
          
          <Text style={styles.confidenceText}>
            置信度: {Math.round(item.confidence * 100)}%
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
        </TouchableOpacity>
        <Text style={styles.title}>预览导入</Text>
        <TouchableOpacity
          style={styles.selectAllButton}
          onPress={toggleAll}
        >
          <Text style={styles.selectAllText}>
            {selectedRecords.size === result.records.length ? '取消全选' : '全选'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{result.totalRecords}</Text>
          <Text style={styles.statLabel}>总记录</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.selectedCount}</Text>
          <Text style={styles.statLabel}>已选择</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.needsReview}</Text>
          <Text style={styles.statLabel}>需确认</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: '#34C759' }]}>
            ¥{stats.totalAmount.toFixed(2)}
          </Text>
          <Text style={styles.statLabel}>总金额</Text>
        </View>
      </View>

      <FlatList
        data={result.records}
        renderItem={renderRecord}
        keyExtractor={(_, index) => index.toString()}
        style={styles.recordList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
      />

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.importButton, isImporting && styles.disabledButton]}
          onPress={handleImport}
          disabled={isImporting || selectedRecords.size === 0}
        >
          {isImporting ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <>
              <Ionicons name="download-outline" size={20} color="#FFFFFF" />
              <Text style={styles.importButtonText}>
                导入 {stats.selectedCount} 条记录
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  selectAllButton: {
    padding: 8,
  },
  selectAllText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8E8E93',
  },
  recordList: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    gap: 12,
  },
  recordCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  selectedCard: {
    borderColor: '#007AFF',
    backgroundColor: '#F0F8FF',
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  recordInfo: {
    flex: 1,
    marginRight: 12,
  },
  recordDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  recordDate: {
    fontSize: 14,
    color: '#8E8E93',
  },
  recordAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  reviewBadge: {
    backgroundColor: '#FF9500',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  reviewText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  recordFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    color: '#FF9500',
  },
  confidenceText: {
    fontSize: 12,
    color: '#8E8E93',
  },
  footer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  importButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#8E8E93',
  },
  importButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

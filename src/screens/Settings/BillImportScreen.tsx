import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import {
  BillImportService,
  BILL_IMPORT_SOURCES,
} from '../../services/billImportService';
import { BillImportSource } from '../../types';

export default function BillImportScreen({ navigation }: any) {
  const { user } = useAppStore();
  const { accounts, loadAccounts } = useTransactionStore();
  const [isImporting, setIsImporting] = useState(false);
  const [selectedSource, setSelectedSource] = useState<BillImportSource | null>(
    null
  );

  // 加载账户数据
  useEffect(() => {
    if (user) {
      loadAccounts(user.id);
    }
  }, [user, loadAccounts]);

  const handleSelectFile = async (source: BillImportSource) => {
    if (!user) {
      Alert.alert('错误', '用户信息不存在');
      return;
    }

    if (accounts.length === 0) {
      Alert.alert('错误', '请先创建至少一个账户');
      return;
    }

    setSelectedSource(source);
    setIsImporting(true);

    try {
      // 选择文件
      const { content, name } = await BillImportService.selectBillFile();

      // 导入账单
      const result = await BillImportService.importBill(
        content,
        name,
        user.id,
        accounts[0].id // 使用第一个账户作为默认账户
      );

      // 导航到预览页面
      navigation.navigate('BillImportPreview', { result });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导入失败';
      if (errorMessage !== '用户取消选择' && errorMessage !== '用户取消') {
        Alert.alert('导入失败', errorMessage);
      }
    } finally {
      setIsImporting(false);
      setSelectedSource(null);
    }
  };

  const renderSourceCard = (source: BillImportSource) => (
    <TouchableOpacity
      key={source.type}
      style={[styles.sourceCard, { borderColor: source.color }]}
      onPress={() => handleSelectFile(source)}
      disabled={isImporting}
    >
      <View style={styles.sourceHeader}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: source.color + '20' },
          ]}
        >
          <Ionicons name={source.icon as any} size={24} color={source.color} />
        </View>
        <View style={styles.sourceInfo}>
          <Text style={styles.sourceName}>{source.name}</Text>
          <Text style={styles.sourceDescription}>{source.description}</Text>
        </View>
        {isImporting && selectedSource?.type === source.type ? (
          <ActivityIndicator size="small" color={source.color} />
        ) : (
          <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
        )}
      </View>

      <View style={styles.formatList}>
        <Text style={styles.formatLabel}>支持格式：</Text>
        {source.supportedFormats.map((format) => (
          <View key={format} style={styles.formatTag}>
            <Text style={styles.formatText}>{format}</Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
        </TouchableOpacity>
        <Text style={styles.title}>导入账单</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>选择账单来源</Text>
          <Text style={styles.sectionDescription}>
            选择您要导入的账单类型，支持主流支付平台和银行的账单格式
          </Text>
        </View>

        <View style={styles.sourceList}>
          {BILL_IMPORT_SOURCES.map(renderSourceCard)}
        </View>

        <View style={styles.helpSection}>
          <View style={styles.helpHeader}>
            <Ionicons name="help-circle-outline" size={20} color="#007AFF" />
            <Text style={styles.helpTitle}>导入说明</Text>
          </View>

          <View style={styles.helpContent}>
            <Text style={styles.helpText}>
              • 支付宝：在支付宝App中导出账单，选择CSV或Excel格式
            </Text>
            <Text style={styles.helpText}>
              • 微信：在微信支付中导出账单，选择CSV或Excel格式
            </Text>
            <Text style={styles.helpText}>
              • 银行：从网银或手机银行导出交易明细
            </Text>
            <Text style={styles.helpText}>
              • 导入后可以预览和编辑交易记录再保存
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#8E8E93',
    lineHeight: 22,
  },
  sourceList: {
    gap: 16,
    marginBottom: 32,
  },
  sourceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  sourceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sourceInfo: {
    flex: 1,
  },
  sourceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  sourceDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18,
  },
  formatList: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 8,
  },
  formatLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginRight: 4,
  },
  formatTag: {
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  formatText: {
    fontSize: 12,
    color: '#1C1C1E',
    fontWeight: '500',
  },
  helpSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  helpHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginLeft: 8,
  },
  helpContent: {
    gap: 8,
  },
  helpText: {
    fontSize: 14,
    color: '#1C1C1E',
    lineHeight: 20,
  },
});

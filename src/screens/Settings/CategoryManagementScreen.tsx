import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import {
  TextInput,
  Picker,
  PickerOption,
  IconPicker,
  ColorPicker,
} from '../../components/forms';
import {
  Button,
  LoadingSpinner,
  EmptyState,
  Card,
} from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { Category } from '../../types';

const CategoryManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAppStore();
  const {
    categories,
    isLoading,
    isCreating,
    isUpdating,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
  } = useTransactionStore();

  const [selectedType, setSelectedType] = useState<'income' | 'expense'>(
    'expense'
  );
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'expense' as 'income' | 'expense',
    icon: 'pricetag',
    color: '#007AFF',
  });
  const [errors, setErrors] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (user) {
      loadCategories(user.id);
    }
  }, [user]);

  const typeOptions: PickerOption[] = [
    {
      label: '支出',
      value: 'expense',
      icon: 'arrow-up-circle',
      color: '#FF3B30',
    },
    {
      label: '收入',
      value: 'income',
      icon: 'arrow-down-circle',
      color: '#34C759',
    },
  ];

  const filteredCategories = categories.filter(
    (category) => category.type === selectedType
  );

  const resetForm = () => {
    setFormData({
      name: '',
      type: selectedType,
      icon: 'pricetag',
      color: '#007AFF',
    });
    setErrors({});
    setEditingCategory(null);
  };

  const handleAdd = () => {
    resetForm();
    setShowAddModal(true);
  };

  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
      type: category.type,
      icon: category.icon,
      color: category.color,
    });
    setEditingCategory(category);
    setShowAddModal(true);
  };

  const handleDelete = (category: Category) => {
    Alert.alert(
      '确认删除',
      `确定要删除分类"${category.name}"吗？此操作无法撤销。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteCategory(category.id);
              Alert.alert('成功', '分类已删除');
            } catch (error) {
              Alert.alert('错误', '删除分类失败，请重试');
            }
          },
        },
      ]
    );
  };

  const validateForm = (): boolean => {
    const newErrors: any = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入分类名称';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setIsSubmitting(true);
    try {
      const categoryData = {
        user_id: user.id,
        name: formData.name,
        type: formData.type,
        icon: formData.icon,
        color: formData.color,
        is_active: true,
      };

      if (editingCategory) {
        await updateCategory(editingCategory.id, categoryData);
        Alert.alert('成功', '分类已更新');
      } else {
        await createCategory(categoryData);
        Alert.alert('成功', '分类已创建');
      }

      setShowAddModal(false);
      resetForm();
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <Card style={styles.categoryCard}>
      <View style={styles.categoryHeader}>
        <View style={styles.categoryLeft}>
          <View
            style={[
              styles.categoryIcon,
              { backgroundColor: item.color + '20' },
            ]}
          >
            <Ionicons
              name={item.icon as keyof typeof Ionicons.glyphMap}
              size={24}
              color={item.color}
            />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{item.name}</Text>
            <Text style={styles.categoryType}>
              {item.type === 'income' ? '收入分类' : '支出分类'}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.categoryActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="create-outline" size={16} color="#007AFF" />
          <Text style={styles.actionButtonText}>编辑</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash-outline" size={16} color="#FF3B30" />
          <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
            删除
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderAddModal = () => (
    <Modal
      visible={showAddModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowAddModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingCategory ? '编辑分类' : '添加分类'}
            </Text>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Ionicons name="close" size={24} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalBody}>
            <TextInput
              label="分类名称"
              value={formData.name}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, name: text }))
              }
              placeholder="请输入分类名称"
              error={errors.name}
              required
            />

            <Picker
              label="分类类型"
              value={formData.type}
              options={typeOptions}
              onSelect={(option) =>
                setFormData((prev) => ({
                  ...prev,
                  type: option.value as 'income' | 'expense',
                }))
              }
              required
            />

            <IconPicker
              label="图标"
              value={formData.icon}
              onSelect={(icon) => setFormData((prev) => ({ ...prev, icon }))}
              required
            />

            <ColorPicker
              label="颜色"
              value={formData.color}
              onSelect={(color) => setFormData((prev) => ({ ...prev, color }))}
              required
            />
          </View>

          <View style={styles.modalFooter}>
            <Button
              title="取消"
              variant="outline"
              onPress={() => setShowAddModal(false)}
              style={styles.modalButton}
            />
            <Button
              title={editingCategory ? '更新' : '添加'}
              onPress={handleSubmit}
              loading={isSubmitting || isCreating || isUpdating}
              disabled={isSubmitting || isCreating || isUpdating}
              style={styles.modalButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading && categories.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>分类管理</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAdd}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* 类型切换 */}
      <View style={styles.typeSelector}>
        {typeOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.typeButton,
              selectedType === option.value && styles.selectedTypeButton,
            ]}
            onPress={() =>
              setSelectedType(option.value as 'income' | 'expense')
            }
          >
            <Ionicons
              name={option.icon as keyof typeof Ionicons.glyphMap}
              size={20}
              color={selectedType === option.value ? '#FFFFFF' : option.color}
            />
            <Text
              style={[
                styles.typeButtonText,
                selectedType === option.value && styles.selectedTypeButtonText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {filteredCategories.length === 0 ? (
        <EmptyState
          icon="pricetag-outline"
          title="暂无分类"
          description={`还没有添加任何${
            selectedType === 'income' ? '收入' : '支出'
          }分类，快去添加第一个分类吧！`}
          actionTitle="添加分类"
          onAction={handleAdd}
        />
      ) : (
        <FlatList
          data={filteredCategories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}

      {renderAddModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeSelector: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  selectedTypeButton: {
    backgroundColor: '#007AFF',
  },
  typeButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedTypeButtonText: {
    color: '#FFFFFF',
  },
  listContainer: {
    padding: 20,
    paddingTop: 0,
  },
  categoryCard: {
    marginBottom: 16,
    padding: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  categoryType: {
    fontSize: 14,
    color: '#8E8E93',
  },
  categoryActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F0F8FF',
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  deleteButton: {
    backgroundColor: '#FFE5E5',
  },
  deleteButtonText: {
    color: '#FF3B30',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalBody: {
    padding: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingBottom: 34,
  },
  modalButton: {
    flex: 1,
  },
});

export default CategoryManagementScreen;

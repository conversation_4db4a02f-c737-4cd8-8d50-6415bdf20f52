import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { TextInput, Picker, PickerOption } from '../../components/forms';
import {
  Button,
  LoadingSpinner,
  EmptyState,
  Card,
} from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { Account } from '../../types';
import { DEFAULT_ACCOUNT_TYPES } from '../../utils/constants';

const AccountManagementScreen: React.FC = () => {
  const navigation = useNavigation();
  const { user } = useAppStore();
  const {
    accounts,
    isLoading,
    isCreating,
    isUpdating,
    loadAccounts,
    createAccount,
    updateAccount,
    deleteAccount,
  } = useTransactionStore();

  const [showAddModal, setShowAddModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    type: 'cash',
    balance: '',
    currency: 'CNY',
  });
  const [errors, setErrors] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (user) {
      loadAccounts(user.id);
    }
  }, [user]);

  const accountTypeOptions: PickerOption[] = DEFAULT_ACCOUNT_TYPES.map(
    (type) => ({
      label: type.name,
      value: type.type,
      icon: type.icon as keyof typeof Ionicons.glyphMap,
      color: type.color,
    })
  );

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'cash',
      balance: '',
      currency: 'CNY',
    });
    setErrors({});
    setEditingAccount(null);
  };

  const handleAdd = () => {
    resetForm();
    setShowAddModal(true);
  };

  const handleEdit = (account: Account) => {
    setFormData({
      name: account.name,
      type: account.type,
      balance: account.balance.toString(),
      currency: account.currency,
    });
    setEditingAccount(account);
    setShowAddModal(true);
  };

  const handleDelete = (account: Account) => {
    Alert.alert(
      '确认删除',
      `确定要删除账户"${account.name}"吗？此操作无法撤销。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAccount(account.id);
              Alert.alert('成功', '账户已删除');
            } catch (error) {
              Alert.alert('错误', '删除账户失败，请重试');
            }
          },
        },
      ]
    );
  };

  const validateForm = (): boolean => {
    const newErrors: any = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入账户名称';
    }

    if (!formData.balance || parseFloat(formData.balance) < 0) {
      newErrors.balance = '请输入有效余额';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setIsSubmitting(true);
    try {
      const accountData = {
        user_id: user.id,
        name: formData.name,
        type: formData.type as Account['type'],
        balance: parseFloat(formData.balance),
        currency: formData.currency,
        is_active: true,
      };

      if (editingAccount) {
        await updateAccount(editingAccount.id, accountData);
        Alert.alert('成功', '账户已更新');
      } else {
        await createAccount(accountData);
        Alert.alert('成功', '账户已创建');
      }

      setShowAddModal(false);
      resetForm();
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAccountIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    const accountType = DEFAULT_ACCOUNT_TYPES.find((t) => t.type === type);
    return (
      (accountType?.icon as keyof typeof Ionicons.glyphMap) || 'wallet-outline'
    );
  };

  const getAccountColor = (type: string): string => {
    const accountType = DEFAULT_ACCOUNT_TYPES.find((t) => t.type === type);
    return accountType?.color || '#8E8E93';
  };

  const renderAccount = ({ item }: { item: Account }) => (
    <Card style={styles.accountCard}>
      <View style={styles.accountHeader}>
        <View style={styles.accountLeft}>
          <View
            style={[
              styles.accountIcon,
              { backgroundColor: getAccountColor(item.type) + '20' },
            ]}
          >
            <Ionicons
              name={getAccountIcon(item.type)}
              size={24}
              color={getAccountColor(item.type)}
            />
          </View>
          <View style={styles.accountInfo}>
            <Text style={styles.accountName}>{item.name}</Text>
            <Text style={styles.accountType}>
              {DEFAULT_ACCOUNT_TYPES.find((t) => t.type === item.type)?.name ||
                item.type}
            </Text>
          </View>
        </View>

        <View style={styles.accountRight}>
          <Text style={styles.accountBalance}>¥{item.balance.toFixed(2)}</Text>
          <Text style={styles.accountCurrency}>{item.currency}</Text>
        </View>
      </View>

      <View style={styles.accountActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleEdit(item)}
        >
          <Ionicons name="create-outline" size={16} color="#007AFF" />
          <Text style={styles.actionButtonText}>编辑</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Ionicons name="trash-outline" size={16} color="#FF3B30" />
          <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
            删除
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderAddModal = () => (
    <Modal
      visible={showAddModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowAddModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {editingAccount ? '编辑账户' : '添加账户'}
            </Text>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Ionicons name="close" size={24} color="#8E8E93" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalBody}>
            <TextInput
              label="账户名称"
              value={formData.name}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, name: text }))
              }
              placeholder="请输入账户名称"
              error={errors.name}
              required
            />

            <Picker
              label="账户类型"
              value={formData.type}
              options={accountTypeOptions}
              onSelect={(option) =>
                setFormData((prev) => ({ ...prev, type: option.value }))
              }
              required
            />

            <TextInput
              label="初始余额"
              value={formData.balance}
              onChangeText={(text) => {
                const numericText = text.replace(/[^0-9.]/g, '');
                setFormData((prev) => ({ ...prev, balance: numericText }));
              }}
              placeholder="0.00"
              keyboardType="decimal-pad"
              error={errors.balance}
              required
            />
          </View>

          <View style={styles.modalFooter}>
            <Button
              title="取消"
              variant="outline"
              onPress={() => setShowAddModal(false)}
              style={styles.modalButton}
            />
            <Button
              title={editingAccount ? '更新' : '添加'}
              onPress={handleSubmit}
              loading={isSubmitting || isCreating || isUpdating}
              disabled={isSubmitting || isCreating || isUpdating}
              style={styles.modalButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading && accounts.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>账户管理</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAdd}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {accounts.length === 0 ? (
        <EmptyState
          icon="wallet-outline"
          title="暂无账户"
          description="还没有添加任何账户，快去添加第一个账户吧！"
          actionTitle="添加账户"
          onAction={handleAdd}
        />
      ) : (
        <FlatList
          data={accounts}
          renderItem={renderAccount}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}

      {renderAddModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
  },
  accountCard: {
    marginBottom: 16,
    padding: 16,
  },
  accountHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  accountType: {
    fontSize: 14,
    color: '#8E8E93',
  },
  accountRight: {
    alignItems: 'flex-end',
  },
  accountBalance: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  accountCurrency: {
    fontSize: 14,
    color: '#8E8E93',
  },
  accountActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F0F8FF',
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  deleteButton: {
    backgroundColor: '#FFE5E5',
  },
  deleteButtonText: {
    color: '#FF3B30',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  modalBody: {
    padding: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
    paddingBottom: 34,
  },
  modalButton: {
    flex: 1,
  },
});

export default AccountManagementScreen;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import {
  TextInput,
  Picker,
  DatePicker,
  PickerOption,
} from '../../components/forms';
import { <PERSON>ton, LoadingSpinner } from '../../components/common';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { TransactionFormData, AIParseResult, Transaction } from '../../types';

interface AddTransactionScreenProps {
  route?: {
    params?: {
      aiData?: AIParseResult;
      editTransaction?: Transaction;
    };
  };
}

const AddTransactionScreen: React.FC<AddTransactionScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAppStore();
  const {
    accounts,
    categories,
    isLoading,
    createTransaction,
    updateTransaction,
    loadAccounts,
    loadCategories,
  } = useTransactionStore();

  const { aiData, editTransaction } = (route.params as any) || {};
  const isEditing = !!editTransaction;

  const [formData, setFormData] = useState<TransactionFormData>({
    amount: '',
    type: 'expense',
    category_id: '',
    account_id: '',
    description: '',
    notes: '',
    transaction_date: new Date(),
    tags: [],
  });

  const [errors, setErrors] = useState<Partial<TransactionFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (user) {
      loadAccounts(user.id);
      loadCategories(user.id);
    }
  }, [user]);

  useEffect(() => {
    // 如果有AI数据，预填充表单
    if (aiData) {
      setFormData((prev) => ({
        ...prev,
        amount: aiData.amount.toString(),
        type: aiData.type,
        description: aiData.description || aiData.merchant || '',
      }));
    }

    // 如果是编辑模式，预填充表单
    if (editTransaction) {
      setFormData({
        amount: editTransaction.amount.toString(),
        type: editTransaction.type,
        category_id: editTransaction.category_id,
        account_id: editTransaction.account_id,
        description: editTransaction.description || '',
        notes: editTransaction.notes || '',
        transaction_date: new Date(editTransaction.transaction_date),
        tags: editTransaction.tags || [],
      });
    }
  }, [aiData, editTransaction]);

  const typeOptions: PickerOption[] = [
    {
      label: '支出',
      value: 'expense',
      icon: 'arrow-up-circle',
      color: '#FF3B30',
    },
    {
      label: '收入',
      value: 'income',
      icon: 'arrow-down-circle',
      color: '#34C759',
    },
  ];

  const accountOptions: PickerOption[] = accounts.map((account) => ({
    label: `${account.name} (¥${account.balance.toFixed(2)})`,
    value: account.id,
    icon: getAccountIcon(account.type),
  }));

  const categoryOptions: PickerOption[] = categories
    .filter((category) => category.type === formData.type)
    .map((category) => ({
      label: category.name,
      value: category.id,
      icon: category.icon as keyof typeof Ionicons.glyphMap,
      color: category.color,
    }));

  function getAccountIcon(type: string): keyof typeof Ionicons.glyphMap {
    switch (type) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'card-outline';
      case 'alipay':
        return 'phone-portrait-outline';
      case 'wechat':
        return 'chatbubble-outline';
      case 'credit_card':
        return 'card-outline';
      default:
        return 'wallet-outline';
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<TransactionFormData> = {};

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = '请输入有效金额';
    }

    if (!formData.category_id) {
      newErrors.category_id = '请选择分类';
    }

    if (!formData.account_id) {
      newErrors.account_id = '请选择账户';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setIsSubmitting(true);

    try {
      const transactionData = {
        user_id: user.id,
        account_id: formData.account_id,
        category_id: formData.category_id,
        amount: parseFloat(formData.amount),
        type: formData.type,
        description: formData.description,
        notes: formData.notes,
        transaction_date: formData.transaction_date.toISOString(),
        tags: formData.tags,
        is_ai_generated: !!aiData,
        ai_confidence: aiData?.confidence,
      };

      if (isEditing && editTransaction) {
        await updateTransaction(editTransaction.id, transactionData);
        Alert.alert('成功', '交易记录已更新');
      } else {
        await createTransaction(transactionData);
        Alert.alert('成功', '记账完成！');
      }

      navigation.goBack();
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading && accounts.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {aiData && (
            <View style={styles.aiNotice}>
              <Ionicons name="flash-outline" size={20} color="#007AFF" />
              <Text style={styles.aiNoticeText}>
                AI识别结果，请确认信息是否正确
              </Text>
            </View>
          )}

          <Picker
            label="交易类型"
            value={formData.type}
            options={typeOptions}
            onSelect={(option) => {
              setFormData((prev) => ({
                ...prev,
                type: option.value as 'income' | 'expense',
                category_id: '', // 重置分类选择
              }));
            }}
            required
          />

          <TextInput
            label="金额"
            value={formData.amount}
            onChangeText={(text) => {
              // 只允许数字和小数点
              const numericText = text.replace(/[^0-9.]/g, '');
              setFormData((prev) => ({ ...prev, amount: numericText }));
            }}
            placeholder="0.00"
            keyboardType="decimal-pad"
            leftIcon="cash-outline"
            error={errors.amount}
            required
          />

          <Picker
            label="分类"
            value={formData.category_id}
            options={categoryOptions}
            onSelect={(option) => {
              setFormData((prev) => ({ ...prev, category_id: option.value }));
            }}
            placeholder="选择分类"
            error={errors.category_id}
            required
          />

          <Picker
            label="账户"
            value={formData.account_id}
            options={accountOptions}
            onSelect={(option) => {
              setFormData((prev) => ({ ...prev, account_id: option.value }));
            }}
            placeholder="选择账户"
            error={errors.account_id}
            required
          />

          <TextInput
            label="描述"
            value={formData.description}
            onChangeText={(text) => {
              setFormData((prev) => ({ ...prev, description: text }));
            }}
            placeholder="添加描述（可选）"
            leftIcon="text-outline"
          />

          <DatePicker
            label="交易日期"
            value={formData.transaction_date}
            onChange={(date) => {
              setFormData((prev) => ({ ...prev, transaction_date: date }));
            }}
            maximumDate={new Date()}
          />

          <TextInput
            label="备注"
            value={formData.notes}
            onChangeText={(text) => {
              setFormData((prev) => ({ ...prev, notes: text }));
            }}
            placeholder="添加备注（可选）"
            multiline
            numberOfLines={3}
            leftIcon="document-text-outline"
          />
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={isEditing ? '更新记录' : '保存记录'}
          onPress={handleSubmit}
          loading={isSubmitting}
          disabled={isSubmitting}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  aiNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  aiNoticeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    paddingBottom: 34,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
});

export default AddTransactionScreen;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import {
  Card,
  LoadingSpinner,
  EmptyState,
  SafeAreaWrapper,
  useSafeAreaInfo,
} from '../../components/common';
import { PieChart, TrendChart, BudgetChart } from '../../components/charts';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import {
  MonthlyStats,
  CategoryStats,
  TrendData,
  BudgetStats,
} from '../../types';

const { width: screenWidth } = Dimensions.get('window');

const StatsScreen: React.FC = () => {
  const { user } = useAppStore();
  const {
    transactions,
    accounts,
    categories,
    loadTransactions,
    loadAccounts,
    loadCategories,
  } = useTransactionStore();

  const { insets, hasNotch, contentWidth } = useSafeAreaInfo();

  const [selectedPeriod, setSelectedPeriod] = useState<
    'week' | 'month' | 'year'
  >('month');
  const [selectedTab, setSelectedTab] = useState<
    'overview' | 'trends' | 'budget'
  >('overview');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      await Promise.all([
        loadTransactions(user.id, true),
        loadAccounts(user.id),
        loadCategories(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load stats data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 计算统计数据
  const calculateStats = () => {
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
    }

    const filteredTransactions = transactions.filter(
      (t) => new Date(t.transaction_date) >= startDate
    );

    const totalIncome = filteredTransactions
      .filter((t) => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpense = filteredTransactions
      .filter((t) => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const netAmount = totalIncome - totalExpense;

    // 按分类统计
    const categoryStats: { [key: string]: CategoryStats } = {};

    filteredTransactions.forEach((transaction) => {
      const categoryId = transaction.category_id;
      const category = categories.find((c) => c.id === categoryId);

      if (!categoryStats[categoryId]) {
        categoryStats[categoryId] = {
          category_id: categoryId,
          category_name: category?.name || '未知分类',
          total_amount: 0,
          transaction_count: 0,
          percentage: 0,
          trend: 'stable',
        };
      }

      categoryStats[categoryId].total_amount += transaction.amount;
      categoryStats[categoryId].transaction_count += 1;
    });

    // 计算百分比
    const totalAmount = totalIncome + totalExpense;
    Object.values(categoryStats).forEach((stat) => {
      stat.percentage =
        totalAmount > 0 ? (stat.total_amount / totalAmount) * 100 : 0;
    });

    const topCategories = Object.values(categoryStats)
      .sort((a, b) => b.total_amount - a.total_amount)
      .slice(0, 5);

    return {
      totalIncome,
      totalExpense,
      netAmount,
      transactionCount: filteredTransactions.length,
      topCategories,
    };
  };

  const stats = calculateStats();

  // 计算趋势数据
  const calculateTrendData = (): TrendData[] => {
    const now = new Date();
    const trendData: TrendData[] = [];

    // 根据选择的时间段生成数据点
    const periods =
      selectedPeriod === 'week' ? 7 : selectedPeriod === 'month' ? 12 : 12;

    for (let i = periods - 1; i >= 0; i--) {
      let startDate: Date;
      let endDate: Date;
      let label: string;

      if (selectedPeriod === 'week') {
        startDate = new Date(now.getTime() - (i + 1) * 24 * 60 * 60 * 1000);
        endDate = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        label = `${startDate.getMonth() + 1}/${startDate.getDate()}`;
      } else if (selectedPeriod === 'month') {
        startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
        label = `${startDate.getMonth() + 1}月`;
      } else {
        startDate = new Date(now.getFullYear() - i, 0, 1);
        endDate = new Date(now.getFullYear() - i + 1, 0, 0);
        label = `${startDate.getFullYear()}年`;
      }

      const periodTransactions = transactions.filter((t) => {
        const date = new Date(t.transaction_date);
        return date >= startDate && date <= endDate;
      });

      const income = periodTransactions
        .filter((t) => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);

      const expense = periodTransactions
        .filter((t) => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      trendData.push({
        period: label,
        income,
        expense,
        net: income - expense,
        transaction_count: periodTransactions.length,
      });
    }

    return trendData;
  };

  // 计算预算数据（模拟数据，实际应该从数据库获取）
  const calculateBudgetData = (): BudgetStats[] => {
    const budgetData: BudgetStats[] = [];

    // 获取当月数据
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const monthlyTransactions = transactions.filter(
      (t) =>
        new Date(t.transaction_date) >= startOfMonth && t.type === 'expense'
    );

    // 按分类统计支出
    const categoryExpenses: { [key: string]: number } = {};
    monthlyTransactions.forEach((t) => {
      categoryExpenses[t.category_id] =
        (categoryExpenses[t.category_id] || 0) + t.amount;
    });

    // 模拟预算设置（实际应该从数据库获取）
    const mockBudgets = [
      { category_id: 'food', budget: 2000 },
      { category_id: 'transport', budget: 800 },
      { category_id: 'entertainment', budget: 1000 },
      { category_id: 'shopping', budget: 1500 },
    ];

    mockBudgets.forEach((budget) => {
      const category = categories.find((c) => c.id === budget.category_id);
      const spent = categoryExpenses[budget.category_id] || 0;

      if (category) {
        budgetData.push({
          category_id: budget.category_id,
          category_name: category.name,
          budget_amount: budget.budget,
          spent_amount: spent,
          remaining_amount: Math.max(0, budget.budget - spent),
          progress_percentage: (spent / budget.budget) * 100,
          status:
            spent >= budget.budget
              ? 'exceeded'
              : spent >= budget.budget * 0.8
              ? 'warning'
              : 'safe',
          color: category.color,
          icon: category.icon,
        });
      }
    });

    return budgetData;
  };

  const trendData = calculateTrendData();
  const budgetData = calculateBudgetData();

  const periodOptions = [
    { label: '本周', value: 'week' as const },
    { label: '本月', value: 'month' as const },
    { label: '本年', value: 'year' as const },
  ];

  const tabOptions = [
    { label: '概览', value: 'overview' as const, icon: 'analytics' },
    { label: '趋势', value: 'trends' as const, icon: 'trending-up' },
    { label: '预算', value: 'budget' as const, icon: 'wallet' },
  ];

  const renderOverviewCard = () => (
    <Card style={styles.overviewCard}>
      <Text style={styles.cardTitle}>财务概览</Text>

      <View style={styles.overviewGrid}>
        <View style={styles.overviewItem}>
          <View
            style={[styles.overviewIcon, { backgroundColor: '#34C759' + '20' }]}
          >
            <Ionicons name="arrow-down-circle" size={24} color="#34C759" />
          </View>
          <Text style={styles.overviewLabel}>收入</Text>
          <Text style={[styles.overviewAmount, { color: '#34C759' }]}>
            ¥{stats.totalIncome.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View
            style={[styles.overviewIcon, { backgroundColor: '#FF3B30' + '20' }]}
          >
            <Ionicons name="arrow-up-circle" size={24} color="#FF3B30" />
          </View>
          <Text style={styles.overviewLabel}>支出</Text>
          <Text style={[styles.overviewAmount, { color: '#FF3B30' }]}>
            ¥{stats.totalExpense.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View
            style={[styles.overviewIcon, { backgroundColor: '#007AFF' + '20' }]}
          >
            <Ionicons name="trending-up" size={24} color="#007AFF" />
          </View>
          <Text style={styles.overviewLabel}>净收入</Text>
          <Text
            style={[
              styles.overviewAmount,
              { color: stats.netAmount >= 0 ? '#34C759' : '#FF3B30' },
            ]}
          >
            ¥{stats.netAmount.toFixed(2)}
          </Text>
        </View>

        <View style={styles.overviewItem}>
          <View
            style={[styles.overviewIcon, { backgroundColor: '#8E8E93' + '20' }]}
          >
            <Ionicons name="receipt" size={24} color="#8E8E93" />
          </View>
          <Text style={styles.overviewLabel}>交易笔数</Text>
          <Text style={styles.overviewAmount}>{stats.transactionCount}</Text>
        </View>
      </View>
    </Card>
  );

  const renderCategoryChart = () => {
    const pieChartData = stats.topCategories.map((category, index) => {
      const categoryInfo = categories.find(
        (c) => c.id === category.category_id
      );
      const colors = ['#007AFF', '#34C759', '#FF3B30', '#FF9500', '#AF52DE'];

      return {
        name: category.category_name,
        amount: category.total_amount,
        color: categoryInfo?.color || colors[index % colors.length],
      };
    });

    return (
      <PieChart
        data={pieChartData}
        title="分类统计"
        style={styles.chartCard}
        showLegend={true}
      />
    );
  };

  const renderAccountsCard = () => (
    <Card style={styles.accountsCard}>
      <Text style={styles.cardTitle}>账户余额</Text>

      {accounts.length === 0 ? (
        <View style={styles.emptyChart}>
          <Ionicons name="wallet-outline" size={48} color="#C7C7CC" />
          <Text style={styles.emptyChartText}>暂无账户</Text>
        </View>
      ) : (
        <View style={styles.accountsList}>
          {accounts.map((account) => (
            <View key={account.id} style={styles.accountItem}>
              <View style={styles.accountLeft}>
                <View style={styles.accountIcon}>
                  <Ionicons
                    name={getAccountIcon(account.type)}
                    size={20}
                    color="#007AFF"
                  />
                </View>
                <Text style={styles.accountName}>{account.name}</Text>
              </View>
              <Text style={styles.accountBalance}>
                ¥{account.balance.toFixed(2)}
              </Text>
            </View>
          ))}

          <View style={[styles.accountItem, styles.totalAccount]}>
            <Text style={styles.totalLabel}>总余额</Text>
            <Text style={styles.totalBalance}>
              ¥{accounts.reduce((sum, acc) => sum + acc.balance, 0).toFixed(2)}
            </Text>
          </View>
        </View>
      )}
    </Card>
  );

  const getAccountIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'cash':
        return 'cash-outline';
      case 'bank':
        return 'card-outline';
      case 'alipay':
        return 'phone-portrait-outline';
      case 'wechat':
        return 'chatbubble-outline';
      case 'credit_card':
        return 'card-outline';
      default:
        return 'wallet-outline';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaWrapper>
        <LoadingSpinner text="加载统计数据..." />
      </SafeAreaWrapper>
    );
  }

  if (transactions.length === 0) {
    return (
      <SafeAreaWrapper>
        <EmptyState
          icon="bar-chart-outline"
          title="暂无统计数据"
          description="还没有任何交易记录，快去记账吧！"
          actionTitle="去记账"
          onAction={() => {
            // TODO: 导航到记账页面
          }}
        />
      </SafeAreaWrapper>
    );
  }

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'overview':
        return (
          <>
            {renderOverviewCard()}
            {renderCategoryChart()}
            {renderAccountsCard()}
          </>
        );
      case 'trends':
        return (
          <TrendChart
            data={trendData}
            title="收支趋势"
            style={styles.chartCard}
            selectedPeriod={selectedPeriod}
          />
        );
      case 'budget':
        return (
          <BudgetChart
            data={budgetData}
            title="预算管理"
            style={styles.chartCard}
            onAddBudget={() => {
              // TODO: 导航到预算设置页面
              console.log('Add budget');
            }}
            onEditBudget={(categoryId) => {
              // TODO: 导航到预算编辑页面
              console.log('Edit budget for category:', categoryId);
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaWrapper backgroundColor="#F2F2F7" statusBarStyle="dark-content">
      {/* 头部标题 */}
      <View style={[styles.header, { paddingTop: hasNotch ? 0 : 20 }]}>
        <Text style={styles.headerTitle}>统计分析</Text>
        <Text style={styles.headerSubtitle}>
          {selectedPeriod === 'week'
            ? '本周'
            : selectedPeriod === 'month'
            ? '本月'
            : '本年'}
          数据概览
        </Text>
      </View>

      {/* 时间段选择器 */}
      <View style={[styles.periodSelector, { width: contentWidth - 40 }]}>
        {periodOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.periodButton,
              selectedPeriod === option.value && styles.selectedPeriodButton,
            ]}
            onPress={() => setSelectedPeriod(option.value)}
          >
            <Text
              style={[
                styles.periodButtonText,
                selectedPeriod === option.value &&
                  styles.selectedPeriodButtonText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 标签页选择器 */}
      <View style={styles.tabSelector}>
        {tabOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.tabButton,
              selectedTab === option.value && styles.selectedTabButton,
            ]}
            onPress={() => setSelectedTab(option.value)}
          >
            <Ionicons
              name={option.icon as keyof typeof Ionicons.glyphMap}
              size={20}
              color={selectedTab === option.value ? '#FFFFFF' : '#8E8E93'}
            />
            <Text
              style={[
                styles.tabButtonText,
                selectedTab === option.value && styles.selectedTabButtonText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {renderTabContent()}
      </ScrollView>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: '#F2F2F7',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#8E8E93',
    fontWeight: '500',
  },
  periodSelector: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 12,
    marginHorizontal: 2,
  },
  selectedPeriodButton: {
    backgroundColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  periodButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#8E8E93',
  },
  selectedPeriodButtonText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  tabSelector: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderRadius: 12,
    gap: 8,
    marginHorizontal: 2,
  },
  selectedTabButton: {
    backgroundColor: '#007AFF',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
  },
  selectedTabButtonText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  overviewCard: {
    margin: 20,
    marginTop: 0,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    justifyContent: 'space-between',
  },
  overviewItem: {
    width: (screenWidth - 112) / 2, // 调整宽度以适应新的边距
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  overviewIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  overviewLabel: {
    fontSize: 15,
    color: '#8E8E93',
    marginBottom: 6,
    fontWeight: '500',
    textAlign: 'center',
  },
  overviewAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1C1C1E',
    textAlign: 'center',
  },
  chartCard: {
    margin: 20,
    marginTop: 0,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyChartText: {
    marginTop: 12,
    fontSize: 16,
    color: '#8E8E93',
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  categoryCount: {
    fontSize: 14,
    color: '#8E8E93',
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 2,
  },
  categoryPercentage: {
    fontSize: 14,
    color: '#8E8E93',
  },
  accountsCard: {
    margin: 20,
    marginTop: 0,
    marginBottom: 20,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  accountsList: {
    gap: 12,
  },
  accountItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  accountName: {
    fontSize: 17,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  accountBalance: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  totalAccount: {
    borderTopWidth: 2,
    borderTopColor: '#F0F0F0',
    paddingTop: 20,
    marginTop: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  totalLabel: {
    fontSize: 19,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  totalBalance: {
    fontSize: 24,
    fontWeight: '800',
    color: '#007AFF',
  },
});

export default StatsScreen;

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../../stores/appStore';
import { useTransactionStore } from '../../stores/transactionStore';
import { useAIStore } from '../../stores/aiStore';
import { AIParseResult } from '../../types';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import AIConfirmCard from '../../components/common/AIConfirmCard';

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { user } = useAppStore();
  const {
    transactions,
    accounts,
    categories,
    isLoading,
    loadTransactions,
    loadAccounts,
    loadCategories,
    createTransaction,
  } = useTransactionStore();

  const {
    lastParseResult,
    isScreenshotListening,
    startScreenshotListening,
    stopScreenshotListening,
    clearLastResult,
  } = useAIStore();

  const [refreshing, setRefreshing] = useState(false);
  const [showAIConfirm, setShowAIConfirm] = useState(false);

  // 计算统计数据
  const todayTransactions = transactions.filter((t) => {
    const today = new Date().toISOString().split('T')[0];
    return t.transaction_date.startsWith(today);
  });

  const todayIncome = todayTransactions
    .filter((t) => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const todayExpense = todayTransactions
    .filter((t) => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalBalance = accounts.reduce(
    (sum, account) => sum + account.balance,
    0
  );

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  useEffect(() => {
    // 监听AI解析结果
    if (lastParseResult && !showAIConfirm) {
      setShowAIConfirm(true);
    }
  }, [lastParseResult]);

  const loadData = async () => {
    if (!user) return;

    try {
      await Promise.all([
        loadTransactions(user.id, true),
        loadAccounts(user.id),
        loadCategories(user.id),
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleToggleScreenshotListening = async () => {
    try {
      if (isScreenshotListening) {
        stopScreenshotListening();
        Alert.alert('已关闭', 'AI截图识别已关闭');
      } else {
        await startScreenshotListening();
        Alert.alert('已开启', 'AI截图识别已开启，截图后会自动分析');
      }
    } catch (error) {
      Alert.alert('错误', error instanceof Error ? error.message : '操作失败');
    }
  };

  const handleAIConfirm = async (aiData: AIParseResult) => {
    try {
      if (!user || accounts.length === 0 || categories.length === 0) {
        Alert.alert('错误', '请先设置账户和分类');
        return;
      }

      // 找到匹配的分类
      const category =
        categories.find(
          (c) => c.name === aiData.category || c.type === aiData.type
        ) || categories.find((c) => c.type === aiData.type);

      if (!category) {
        Alert.alert('错误', '未找到匹配的分类');
        return;
      }

      // 使用第一个账户
      const account = accounts[0];

      const transactionData = {
        user_id: user.id,
        account_id: account.id,
        category_id: category.id,
        amount: aiData.amount,
        type: aiData.type,
        description: aiData.description || aiData.merchant || '',
        transaction_date: new Date().toISOString(),
        is_ai_generated: true,
        ai_confidence: aiData.confidence,
      };

      await createTransaction(transactionData);
      setShowAIConfirm(false);
      clearLastResult();
      Alert.alert('成功', '记账完成！');
    } catch (error) {
      Alert.alert('错误', '记账失败，请重试');
    }
  };

  const handleAICancel = () => {
    setShowAIConfirm(false);
    clearLastResult();
  };

  const handleAIEdit = () => {
    setShowAIConfirm(false);
    navigation.navigate('AddTransaction', { aiData: lastParseResult });
    clearLastResult();
  };

  if (isLoading && transactions.length === 0) {
    return <LoadingSpinner text="加载中..." />;
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* 头部问候 */}
        <View style={styles.header}>
          <Text style={styles.greeting}>你好，{user?.name || '用户'}</Text>
          <Text style={styles.date}>
            {new Date().toLocaleDateString('zh-CN')}
          </Text>
        </View>

        {/* 总余额卡片 */}
        <View style={styles.balanceCard}>
          <Text style={styles.balanceLabel}>总余额</Text>
          <Text style={styles.balanceAmount}>¥{totalBalance.toFixed(2)}</Text>
        </View>

        {/* 今日统计 */}
        <View style={styles.todayStats}>
          <Text style={styles.sectionTitle}>今日统计</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="arrow-down-circle" size={24} color="#34C759" />
              <Text style={styles.statLabel}>收入</Text>
              <Text style={[styles.statAmount, { color: '#34C759' }]}>
                ¥{todayIncome.toFixed(2)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="arrow-up-circle" size={24} color="#FF3B30" />
              <Text style={styles.statLabel}>支出</Text>
              <Text style={[styles.statAmount, { color: '#FF3B30' }]}>
                ¥{todayExpense.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* AI功能区 */}
        <View style={styles.aiSection}>
          <Text style={styles.sectionTitle}>AI智能记账</Text>
          <View style={styles.aiControls}>
            <TouchableOpacity
              style={[
                styles.aiToggle,
                isScreenshotListening && styles.aiToggleActive,
              ]}
              onPress={handleToggleScreenshotListening}
            >
              <Ionicons
                name={isScreenshotListening ? 'eye' : 'eye-off'}
                size={20}
                color={isScreenshotListening ? '#FFFFFF' : '#007AFF'}
              />
              <Text
                style={[
                  styles.aiToggleText,
                  isScreenshotListening && styles.aiToggleTextActive,
                ]}
              >
                {isScreenshotListening ? '截图识别已开启' : '开启截图识别'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 快速操作 */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>快速操作</Text>
          <View style={styles.actionButtons}>
            <Button
              title="手动记账"
              variant="primary"
              onPress={() => navigation.navigate('AddTransaction')}
              style={styles.actionButton}
            />
            <Button
              title="拍照记账"
              variant="outline"
              onPress={() => navigation.navigate('AICapture')}
              style={styles.actionButton}
            />
          </View>
        </View>

        {/* 最近交易 */}
        <View style={styles.recentTransactions}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近交易</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('TransactionList')}
            >
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>
          {transactions.slice(0, 5).map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionLeft}>
                <Ionicons
                  name={
                    transaction.type === 'income'
                      ? 'arrow-down-circle'
                      : 'arrow-up-circle'
                  }
                  size={20}
                  color={transaction.type === 'income' ? '#34C759' : '#FF3B30'}
                />
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionDescription}>
                    {transaction.description || transaction.category?.name}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.transaction_date).toLocaleDateString(
                      'zh-CN'
                    )}
                  </Text>
                </View>
              </View>
              <Text
                style={[
                  styles.transactionAmount,
                  {
                    color:
                      transaction.type === 'income' ? '#34C759' : '#FF3B30',
                  },
                ]}
              >
                {transaction.type === 'income' ? '+' : '-'}¥
                {transaction.amount.toFixed(2)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* AI确认对话框 */}
      {lastParseResult && (
        <AIConfirmCard
          visible={showAIConfirm}
          parseResult={lastParseResult}
          onConfirm={handleAIConfirm}
          onCancel={handleAICancel}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
  },
  date: {
    fontSize: 16,
    color: '#8E8E93',
    marginTop: 4,
  },
  balanceCard: {
    backgroundColor: '#007AFF',
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 8,
  },
  todayStats: {
    margin: 20,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 8,
  },
  statAmount: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 4,
  },
  aiSection: {
    margin: 20,
    marginTop: 0,
  },
  aiControls: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  aiToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  aiToggleActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  aiToggleText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
  },
  aiToggleTextActive: {
    color: '#FFFFFF',
  },
  quickActions: {
    margin: 20,
    marginTop: 0,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  recentTransactions: {
    margin: 20,
    marginTop: 0,
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  transactionItem: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionInfo: {
    marginLeft: 12,
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1C1C1E',
  },
  transactionDate: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default HomeScreen;

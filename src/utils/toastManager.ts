import { Alert } from 'react-native';

interface ToastAction {
  label: string;
  onPress: () => void;
}

export class ToastManager {
  static success(message: string, duration?: number, action?: ToastAction) {
    if (action) {
      Alert.alert('成功', message, [
        { text: '确定', style: 'default' },
        { text: action.label, onPress: action.onPress }
      ]);
    } else {
      Alert.alert('成功', message, [{ text: '确定' }]);
    }
  }

  static error(message: string, duration?: number) {
    Alert.alert('错误', message, [{ text: '确定' }]);
  }

  static info(message: string, duration?: number) {
    Alert.alert('提示', message, [{ text: '确定' }]);
  }

  static warning(message: string, duration?: number) {
    Alert.alert('警告', message, [{ text: '确定' }]);
  }
}

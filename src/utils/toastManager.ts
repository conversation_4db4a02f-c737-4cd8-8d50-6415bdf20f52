import { Alert } from 'react-native';

interface ToastAction {
  label: string;
  onPress: () => void;
}

interface ToastOptions {
  duration?: number;
  action?: ToastAction;
  showProgress?: boolean;
  autoClose?: boolean;
}

export class ToastManager {
  static success(message: string, options?: ToastOptions) {
    if (options?.action) {
      Alert.alert('✅ 成功', message, [
        { text: '确定', style: 'default' },
        { text: options.action.label, onPress: options.action.onPress },
      ]);
    } else {
      Alert.alert('✅ 成功', message, [{ text: '确定' }]);
    }
  }

  static error(message: string, options?: ToastOptions) {
    Alert.alert('❌ 错误', message, [{ text: '确定' }]);
  }

  static info(message: string, options?: ToastOptions) {
    Alert.alert('ℹ️ 提示', message, [{ text: '确定' }]);
  }

  static warning(message: string, options?: ToastOptions) {
    Alert.alert('⚠️ 警告', message, [{ text: '确定' }]);
  }

  static loading(message: string) {
    Alert.alert('⏳ 处理中', message, []);
  }

  static confirm(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) {
    Alert.alert(title, message, [
      {
        text: '取消',
        style: 'cancel',
        onPress: onCancel,
      },
      {
        text: '确定',
        style: 'default',
        onPress: onConfirm,
      },
    ]);
  }

  // 专门为导入功能设计的提醒
  static importSuccess(
    count: number,
    hasErrors: boolean = false,
    onViewRecords?: () => void
  ) {
    const message = hasErrors
      ? `成功导入 ${count} 条记录，部分记录可能需要手动调整`
      : `成功导入 ${count} 条记录`;

    this.success(message, {
      action: onViewRecords
        ? {
            label: '查看记录',
            onPress: onViewRecords,
          }
        : undefined,
    });
  }

  static importWarning(message: string, onProceed: () => void) {
    Alert.alert('⚠️ 导入提醒', message, [
      { text: '取消', style: 'cancel' },
      { text: '继续导入', onPress: onProceed },
    ]);
  }
}

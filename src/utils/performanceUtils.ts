import { InteractionManager, Platform } from 'react-native';

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 延迟执行，等待交互完成
export function runAfterInteractions<T>(callback: () => T): Promise<T> {
  return new Promise((resolve) => {
    InteractionManager.runAfterInteractions(() => {
      resolve(callback());
    });
  });
}

// 批量处理数据
export function batchProcess<T, R>(
  items: T[],
  processor: (item: T) => R,
  batchSize: number = 50,
  delay: number = 0
): Promise<R[]> {
  return new Promise((resolve) => {
    const results: R[] = [];
    let currentIndex = 0;

    const processBatch = () => {
      const endIndex = Math.min(currentIndex + batchSize, items.length);
      
      for (let i = currentIndex; i < endIndex; i++) {
        results.push(processor(items[i]));
      }
      
      currentIndex = endIndex;
      
      if (currentIndex < items.length) {
        if (delay > 0) {
          setTimeout(processBatch, delay);
        } else {
          setImmediate(processBatch);
        }
      } else {
        resolve(results);
      }
    };

    processBatch();
  });
}

// 内存使用监控
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryWarningThreshold = 0.8; // 80%
  private listeners: Array<(usage: number) => void> = [];

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  addListener(callback: (usage: number) => void) {
    this.listeners.push(callback);
  }

  removeListener(callback: (usage: number) => void) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  checkMemoryUsage() {
    if (Platform.OS === 'web') {
      // Web平台内存检查
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        
        if (usage > this.memoryWarningThreshold) {
          this.notifyListeners(usage);
        }
        
        return usage;
      }
    }
    
    // 移动端暂时返回0，实际项目中可以集成原生内存监控
    return 0;
  }

  private notifyListeners(usage: number) {
    this.listeners.forEach(listener => {
      try {
        listener(usage);
      } catch (error) {
        console.warn('Memory monitor listener error:', error);
      }
    });
  }

  startMonitoring(interval: number = 30000) { // 30秒检查一次
    setInterval(() => {
      this.checkMemoryUsage();
    }, interval);
  }
}

// 图片压缩工具
export function compressImageUri(uri: string, quality: number = 0.8): string {
  // 这里可以集成图片压缩库
  // 目前返回原始URI，实际项目中可以使用 expo-image-manipulator
  return uri;
}

// 数据分页工具
export class DataPaginator<T> {
  private data: T[];
  private pageSize: number;
  private currentPage: number = 0;

  constructor(data: T[], pageSize: number = 20) {
    this.data = data;
    this.pageSize = pageSize;
  }

  getPage(page: number): T[] {
    const startIndex = page * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.data.slice(startIndex, endIndex);
  }

  getCurrentPage(): T[] {
    return this.getPage(this.currentPage);
  }

  nextPage(): T[] | null {
    if (this.hasNextPage()) {
      this.currentPage++;
      return this.getCurrentPage();
    }
    return null;
  }

  previousPage(): T[] | null {
    if (this.hasPreviousPage()) {
      this.currentPage--;
      return this.getCurrentPage();
    }
    return null;
  }

  hasNextPage(): boolean {
    return (this.currentPage + 1) * this.pageSize < this.data.length;
  }

  hasPreviousPage(): boolean {
    return this.currentPage > 0;
  }

  getTotalPages(): number {
    return Math.ceil(this.data.length / this.pageSize);
  }

  getCurrentPageNumber(): number {
    return this.currentPage;
  }

  reset() {
    this.currentPage = 0;
  }

  updateData(newData: T[]) {
    this.data = newData;
    this.currentPage = 0;
  }
}

// 缓存管理器
export class CacheManager {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private static readonly DEFAULT_TTL = 5 * 60 * 1000; // 5分钟

  static set(key: string, data: any, ttl: number = this.DEFAULT_TTL) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  static get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    
    if (!cached) {
      return null;
    }

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static has(key: string): boolean {
    return this.get(key) !== null;
  }

  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  static clear() {
    this.cache.clear();
  }

  static cleanup() {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > value.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  static startAutoCleanup(interval: number = 60000) { // 1分钟清理一次
    setInterval(() => {
      this.cleanup();
    }, interval);
  }
}

// 启动性能监控
export function initializePerformanceMonitoring() {
  // 启动内存监控
  const memoryMonitor = MemoryMonitor.getInstance();
  memoryMonitor.addListener((usage) => {
    console.warn(`High memory usage detected: ${(usage * 100).toFixed(1)}%`);
  });
  memoryMonitor.startMonitoring();

  // 启动缓存自动清理
  CacheManager.startAutoCleanup();

  console.log('Performance monitoring initialized');
}
